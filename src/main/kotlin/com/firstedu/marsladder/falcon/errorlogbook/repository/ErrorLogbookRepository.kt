package com.firstedu.marsladder.falcon.errorlogbook.repository

import com.firstedu.marsladder.falcon.errorlogbook.ErrorLogbookStatus
import com.firstedu.marsladder.falcon.errorlogbook.repository.entity.ChallengeCourseEntity
import com.firstedu.marsladder.falcon.errorlogbook.repository.entity.ErrorLogbookCountByCourseEntity
import com.firstedu.marsladder.falcon.errorlogbook.repository.entity.ErrorLogbookEntity
import com.firstedu.marsladder.falcon.errorlogbook.repository.entity.ErrorLogbookWithCourseOutlineEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.Optional

@Repository
interface ErrorLogbookRepository :
    JpaRepository<ErrorLogbookEntity, String>,
    JpaSpecificationExecutor<ErrorLogbookEntity> {
    fun findByCognitoUidAndQuestionId(
        cognitoUid: String,
        questionId: String,
    ): Optional<ErrorLogbookEntity>

    fun findByCognitoUidAndCourseIdAndDeletedAndStatus(
        cognitoUid: String,
        courseId: String,
        deleted: Boolean,
        status: ErrorLogbookStatus,
    ): List<ErrorLogbookEntity>

    fun findByCognitoUidAndCourseIdAndDeletedOrderByCreatedAtDesc(
        cognitoUid: String,
        courseId: String,
        deleted: Boolean = false,
    ): List<ErrorLogbookEntity>

    fun findByCognitoUidAndId(
        cognitoUid: String,
        id: String,
    ): ErrorLogbookEntity?

    fun findByCognitoUidAndIdIn(
        cognitoUid: String,
        errorQuestionsIds: List<String>,
    ): List<ErrorLogbookEntity>

    fun findByCognitoUid(cognitoUid: String): List<ErrorLogbookEntity>

    @Query(
        value = """
            select * from error_logbook where cognito_uid = :cognitoUid AND deleted = false AND status = 'UNRESOLVED' and JSON_OVERLAPS(subtopic_ids, :subTopicIds) order by rand() limit 5
        """,
        nativeQuery = true,
    )
    fun findSubTopicsIncludedErrorLogbooks(
        cognitoUid: String,
        subTopicIds: String,
    ): List<ErrorLogbookEntity>

    fun findTop5ByCognitoUidAndCourseIdAndAreaIdAndStatusAndDeletedOrderById(
        cognitoUid: String,
        courseId: String,
        areaId: String,
        status: ErrorLogbookStatus,
        deleted: Boolean,
    ): List<ErrorLogbookEntity>

    @Query(
        value = """
            select course_id as courseId, area_id as areaId, count(1) as `count` from error_logbook where cognito_uid = :cognitoUid AND deleted = false AND status = 'UNRESOLVED' AND course_id in :subscribedCourseIds group by course_id, area_id order by `count` desc limit 1
        """,
        nativeQuery = true,
    )
    fun findChallengeCourse(
        cognitoUid: String,
        subscribedCourseIds: List<String>,
    ): Optional<ChallengeCourseEntity>

    fun findByCognitoUidAndCourseIdAndDeleted(
        cognitoUid: String,
        courseId: String,
        deleted: Boolean = false,
    ): List<ErrorLogbookEntity>

    @Query(
        value = """
            SELECT el.id AS id, el.cognito_uid AS CognitoUid, el.course_id AS courseId, el.question_id AS originalQuestionId, el.status AS status, el.difficulty AS difficulty, el.deleted AS deleted, 
                REGEXP_REPLACE(JSON_UNQUOTE(el.error_causes), '[\\[\\]" ]', '')          AS errorCauses,
                GROUP_CONCAT(DISTINCT content.id)                                        AS contentIds, 
                GROUP_CONCAT(DISTINCT subtopic.id)                                       AS subTopicIds, 
                GROUP_CONCAT(DISTINCT topic.id)                                          AS topicIds, 
                GROUP_CONCAT(DISTINCT area.id)                                           AS areaIds 
            FROM error_logbook el 
                LEFT JOIN fuxi.question_content qc ON el.question_id = qc.question_id 
                LEFT JOIN course_outline_item content ON content.id = qc.content_id 
                LEFT JOIN course_outline co ON content.course_outline_id = co.id 
                LEFT JOIN course_outline_item subtopic ON subtopic.id = content.parent_id 
                LEFT JOIN course_outline_item topic ON topic.id = subtopic.parent_id 
                LEFT JOIN course_outline_item area ON area.id = topic.parent_id 
            WHERE co.status = 'ACTIVE' 
                AND el.cognito_uid = :cognitoUid 
                AND el.course_id = :courseId 
            GROUP BY el.question_id, el.cognito_uid;
        """,
        nativeQuery = true,
    )
    fun findByCognitoUidAndCourseIdWithCourseOutline(
        cognitoUid: String,
        courseId: String,
    ): List<ErrorLogbookWithCourseOutlineEntity>

    fun findByCognitoUidAndQuestionIdIn(
        cognitoUid: String,
        questionIds: List<String>,
    ): List<ErrorLogbookEntity>

    @Query(
        value = """
            SELECT course_id as courseId, count(1) as unresolvedCount
            FROM error_logbook
            WHERE cognito_uid = :cognitoUid
                AND deleted = false
                AND status = 'UNRESOLVED'
            GROUP BY course_id
        """,
        nativeQuery = true,
    )
    fun countUnresolvedErrorLogbooksByCourse(cognitoUid: String): List<ErrorLogbookCountByCourseEntity>
}
