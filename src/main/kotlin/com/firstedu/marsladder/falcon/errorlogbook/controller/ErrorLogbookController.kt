package com.firstedu.marsladder.falcon.errorlogbook.controller

import com.firstedu.marsladder.falcon.errorlogbook.ErrorLogbookStatus
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.BatchUpdateErrorCausesRequest
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.DeleteErrorQuestionsRequest
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.ErrorLogbookChallengeStatusResponse
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.ErrorLogbookCourseOutlineResponse
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.GetErrorLogbookCountByCourseResponse
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.GetErrorLogbookOutlineTagsResponse
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.GetErrorLogbookResponse
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.GetPracticeRecommendationResponse
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.PracticeFromErrorLogbookRequest
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.UpdateErrorCausesRequest
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.WeakSpotSubTopicResponse
import com.firstedu.marsladder.falcon.course.service.CourseService
import com.firstedu.marsladder.falcon.errorlogbook.service.ErrorLogbookPracticeService
import com.firstedu.marsladder.falcon.errorlogbook.service.ErrorLogbookService
import com.firstedu.marsladder.falcon.exception.NoPermissionException
import com.firstedu.marsladder.falcon.practice.service.exception.InvalidPracticeQuestionException
import com.firstedu.marsladder.falcon.practice.service.exception.NotEnoughQuestionException
import com.firstedu.marsladder.falcon.security.SessionProvider
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.domain.Sort.Direction
import org.springframework.data.web.PageableDefault
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@PreAuthorize("hasRole('CONSUMER')")
@RestController
@RequestMapping("error-logbooks")
class ErrorLogbookController(
    private val errorLogbookService: ErrorLogbookService,
    private val errorLogbookPracticeService: ErrorLogbookPracticeService,
    private val courseService: CourseService,
    private val sessionProvider: SessionProvider,
) {
    @GetMapping("me")
    fun getAll(
        @RequestParam courseId: String,
        @RequestParam areaId: String?,
        @RequestParam topicId: String?,
        @RequestParam subtopicId: String?,
        @RequestParam status: ErrorLogbookStatus?,
        @RequestParam favorite: Boolean?,
        @RequestParam errorCauses: List<String>?,
        @RequestParam sort: Sort.Direction?,
    ): ResponseEntity<List<GetErrorLogbookResponse>> {
        val errorLogbooks =
            errorLogbookService.getAll(
                cognitoUid = sessionProvider.getUserId(),
                courseId = courseId,
                areaId = areaId,
                topicId = topicId,
                subtopicId = subtopicId,
                status = status,
                favorite = favorite,
                errorCauses = errorCauses,
                sort = sort,
            )
        return ResponseEntity.status(HttpStatus.OK).body(errorLogbooks.map(GetErrorLogbookResponse::from))
    }

    @GetMapping("/pageable")
    fun getAllErrorLogbooks(
        @RequestParam courseId: String,
        @RequestParam areaId: String?,
        @RequestParam topicId: String?,
        @RequestParam subtopicId: String?,
        @RequestParam status: ErrorLogbookStatus?,
        @RequestParam favorite: Boolean?,
        @RequestParam errorCauses: List<String>?,
        @PageableDefault(size = 25, page = 0, sort = ["createdAt"], direction = Direction.DESC) pageable: Pageable,
    ): ResponseEntity<Page<GetErrorLogbookResponse>> {
        val errorLogbooks =
            errorLogbookService.getAllErrorLogbooks(
                cognitoUid = sessionProvider.getUserId(),
                courseId = courseId,
                areaId = areaId,
                topicId = topicId,
                subtopicId = subtopicId,
                status = status,
                favorite = favorite,
                errorCauses = errorCauses,
                pageable = pageable,
            )
        return ResponseEntity.status(HttpStatus.OK).body(errorLogbooks.map(GetErrorLogbookResponse::from))
    }

    @PostMapping("/{errorLogbookId}/practices")
    fun createPractice(
        @PathVariable errorLogbookId: String,
    ): ResponseEntity<String> {
        errorLogbookService.hasPermissionToReadOrUpdateErrorLogbook(errorLogbookId, sessionProvider.getUserId())
            .takeIf { it } ?: throw NoPermissionException("Permission deny!")

        try {
            return ResponseEntity.ok(
                errorLogbookPracticeService.createErrorRelatedPractice(
                    sessionProvider.getUserId(),
                    errorLogbookId,
                ),
            )
        } catch (e: NotEnoughQuestionException) {
            throw InvalidPracticeQuestionException("Do not have enough questions")
        }
    }

    @GetMapping("/{errorLogbookId}/outline-tags")
    fun createOutlineTags(
        @PathVariable errorLogbookId: String,
    ): ResponseEntity<GetErrorLogbookOutlineTagsResponse> {
        return ResponseEntity.status(
            HttpStatus.OK,
        ).body(GetErrorLogbookOutlineTagsResponse.from(errorLogbookService.getOutlineTags(errorLogbookId, sessionProvider.getUserId())))
    }

    @PostMapping("/{errorLogbookId}/favorite")
    fun favoriteErrorQuestion(
        @PathVariable errorLogbookId: String,
    ): ResponseEntity<Unit> {
        errorLogbookService.favoriteErrorQuestion(sessionProvider.getUserId(), errorLogbookId)
        return ResponseEntity.noContent().build()
    }

    @DeleteMapping("/{errorLogbookId}/favorite")
    fun unfavoriteErrorQuestion(
        @PathVariable errorLogbookId: String,
    ): ResponseEntity<Unit> {
        errorLogbookService.unfavoriteErrorQuestion(sessionProvider.getUserId(), errorLogbookId)
        return ResponseEntity.noContent().build()
    }

    @DeleteMapping("/{errorLogbookId}")
    fun deleteErrorQuestion(
        @PathVariable errorLogbookId: String,
    ): ResponseEntity<Unit> {
        errorLogbookService.deleteErrorQuestion(sessionProvider.getUserId(), errorLogbookId)
        return ResponseEntity.noContent().build()
    }

    @DeleteMapping
    fun deleteErrorQuestions(
        @Validated @RequestBody deleteErrorQuestionsRequest: DeleteErrorQuestionsRequest,
    ): ResponseEntity<Unit> {
        errorLogbookService.deleteErrorQuestions(
            sessionProvider.getUserId(),
            deleteErrorQuestionsRequest.errorQuestionsIds,
        )
        return ResponseEntity.noContent().build()
    }

    @PutMapping("/{errorLogbookId}/error-causes")
    fun updateErrorCauses(
        @PathVariable errorLogbookId: String,
        @Validated @RequestBody updateErrorCausesRequest: UpdateErrorCausesRequest,
    ): ResponseEntity<Unit> {
        errorLogbookService.updateErrorCauses(
            sessionProvider.getUserId(),
            errorLogbookId,
            updateErrorCausesRequest.errorCauseIds,
        )
        return ResponseEntity.noContent().build()
    }

    @PutMapping("/error-causes")
    fun batchUpdateErrorCauses(
        @Validated @RequestBody batchUpdateErrorCausesRequest: BatchUpdateErrorCausesRequest,
    ): ResponseEntity<Unit> {
        errorLogbookService.batchUpdateErrorCauses(
            sessionProvider.getUserId(),
            batchUpdateErrorCausesRequest.errorQuestionsIds,
            batchUpdateErrorCausesRequest.errorCauseIds,
        )
        return ResponseEntity.noContent().build()
    }

    @PostMapping("/practices")
    fun practiceFromErrorLogbook(
        @RequestBody request: PracticeFromErrorLogbookRequest,
    ): ResponseEntity<String> {
        try {
            return ResponseEntity.ok(
                errorLogbookPracticeService.practiceFromErrorLogbook(
                    sessionProvider.getUserId(),
                    request,
                ),
            )
        } catch (e: NotEnoughQuestionException) {
            throw InvalidPracticeQuestionException("Do not have enough questions")
        }
    }

    @PostMapping("/challenges")
    fun challengeErrorLogbook(): ResponseEntity<String> {
        try {
            return ResponseEntity.ok(errorLogbookPracticeService.challengeErrorLogbook(sessionProvider.getUserId()))
        } catch (e: NotEnoughQuestionException) {
            throw InvalidPracticeQuestionException("Do not have enough questions")
        }
    }

    @GetMapping("/{courseId}/outline")
    fun getErrorLogbookCourseOutline(
        @PathVariable courseId: String,
    ): ResponseEntity<ErrorLogbookCourseOutlineResponse> {
        val courseOutline =
            errorLogbookService.getErrorLogbookCourseOutline(
                sessionProvider.getUserId(),
                courseId,
            )
        return ResponseEntity.status(HttpStatus.OK)
            .body(
                ErrorLogbookCourseOutlineResponse(
                    courseOutlineId = courseOutline.courseOutlineId,
                    areas = courseOutline.areas,
                ),
            )
    }

    @PostMapping("/practices/{practiceId}/replay")
    fun replay(
        @PathVariable practiceId: String,
    ): ResponseEntity<String> =
        ResponseEntity.status(HttpStatus.OK)
            .body(errorLogbookPracticeService.replayPractice(sessionProvider.getUserId(), practiceId))

    @GetMapping("/challenge-status/me")
    fun getChallengeStatus() =
        ResponseEntity.status(HttpStatus.OK).body(
            ErrorLogbookChallengeStatusResponse(
                errorLogbookService.getErrorLogbookChallengeStatus(sessionProvider.getUserId())
                    ?.challengeCourse
                    ?: emptyList(),
            ),
        )

    @GetMapping("/challenge-notifications/me")
    fun getChallengeNotifications() =
        ResponseEntity.status(HttpStatus.OK).body(
            ErrorLogbookChallengeStatusResponse(
                errorLogbookService.getErrorLogbookChallengeStatus(sessionProvider.getUserId())
                    ?.challengeCourse
                    ?.map { courseChallengeStatus ->
                        courseChallengeStatus.takeIf { it.shouldPopUp }?.also {
                            errorLogbookService.disablePopUpOfChallengeStatus(sessionProvider.getUserId(), it.courseId)
                        }
                        courseChallengeStatus
                    }
                    ?: emptyList(),
            ),
        )

    @GetMapping("/{courseId}/practice-recommendations/me")
    fun getPracticeRecommendations(
        @PathVariable courseId: String,
    ) = ResponseEntity.status(HttpStatus.OK).body(
        errorLogbookPracticeService.getRecommendations(sessionProvider.getUserId(), courseId).map {
            GetPracticeRecommendationResponse(
                areaOfStudyId = it.areaOfStudyId,
                weakSpotTop3SubTopics = it.weakSpotTop3SubTopics.map(WeakSpotSubTopicResponse::from),
            )
        },
    )

    @PostMapping("/favorite/{practiceId}/{questionId}")
    fun saveFavoriteQuestion(
        @PathVariable practiceId: String,
        @PathVariable questionId: String,
    ): ResponseEntity<GetErrorLogbookResponse> {
        val errorLogbook = errorLogbookService.saveFavoriteQuestion(practiceId, questionId, sessionProvider.getUserId())
        return ResponseEntity.status(HttpStatus.OK).body(errorLogbook.let { GetErrorLogbookResponse.from(it) })
    }

    @GetMapping("/practices/{practiceId}")
    fun getErrorLogbooksByPracticeId(
        @PathVariable practiceId: String,
    ): ResponseEntity<List<GetErrorLogbookResponse>> {
        val errorLogbooks = errorLogbookService.getErrorLogbooksByPracticeId(practiceId, sessionProvider.getUserId())
        return ResponseEntity.status(HttpStatus.OK).body(errorLogbooks.map(GetErrorLogbookResponse::from))
    }

    @GetMapping("/unresolved-count-by-course/me")
    fun getUnresolvedErrorLogbookCountByCourse(): ResponseEntity<List<GetErrorLogbookCountByCourseResponse>> {
        val cognitoUid = sessionProvider.getUserId()
        val countByCourse = errorLogbookService.getUnresolvedErrorLogbookCountByCourse(cognitoUid)

        val response = countByCourse.map { (courseId, count) ->
            val course = courseService.getCourse(courseId)
            GetErrorLogbookCountByCourseResponse(
                courseId = courseId,
                courseName = course.name,
                courseGrade = course.grade,
                unresolvedCount = count
            )
        }

        return ResponseEntity.status(HttpStatus.OK).body(response)
    }
}
