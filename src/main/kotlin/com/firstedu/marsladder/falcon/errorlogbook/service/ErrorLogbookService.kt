package com.firstedu.marsladder.falcon.errorlogbook.service

import com.firstedu.marsladder.falcon.course.service.domain.CourseOutline
import com.firstedu.marsladder.falcon.errorlogbook.ErrorLogbookStatus
import com.firstedu.marsladder.falcon.errorlogbook.repository.entity.ErrorLogbookChallengeEventEntity
import com.firstedu.marsladder.falcon.errorlogbook.service.domain.ErrorLogbook
import com.firstedu.marsladder.falcon.errorlogbook.service.domain.ErrorLogbookChallengeStatus
import com.firstedu.marsladder.falcon.errorlogbook.service.domain.ErrorLogbookOutlineTags
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort

interface ErrorLogbookService {
    fun getAll(
        cognitoUid: String,
        courseId: String? = null,
        areaId: String? = null,
        topicId: String? = null,
        subtopicId: String? = null,
        status: ErrorLogbookStatus? = null,
        favorite: Boolean? = null,
        errorCauses: List<String>? = null,
        sort: Sort.Direction? = null,
    ): List<ErrorLogbook>

    fun getAllErrorLogbooks(
        cognitoUid: String,
        courseId: String? = null,
        areaId: String? = null,
        topicId: String? = null,
        subtopicId: String? = null,
        status: ErrorLogbookStatus? = null,
        favorite: Boolean? = null,
        errorCauses: List<String>? = null,
        pageable: Pageable,
    ): Page<ErrorLogbook>

    fun savePracticeQuestionsForErrorLogbook(practiceId: String)

    fun hasPermissionToReadOrUpdateErrorLogbook(
        errorLogbookId: String,
        userId: String,
    ): Boolean

    fun getOutlineTags(
        errorLogbookId: String,
        userId: String,
    ): ErrorLogbookOutlineTags

    fun updateErrorCauses(
        userId: String,
        errorLogbookId: String,
        errorCauseIds: List<String>,
    )

    fun favoriteErrorQuestion(
        userId: String,
        errorLogbookId: String,
    )

    fun unfavoriteErrorQuestion(
        userId: String,
        errorLogbookId: String,
    )

    fun deleteErrorQuestion(
        userId: String,
        errorLogbookId: String,
    )

    fun deleteErrorQuestions(
        userId: String,
        errorQuestionsIds: List<String>,
    )

    fun batchUpdateErrorCauses(
        userId: String,
        errorQuestionsIds: List<String>,
        errorCauseIds: List<String>,
    )

    fun getErrorLogbookCourseOutline(
        userId: String,
        courseId: String,
    ): CourseOutline

    fun refreshErrorLogbookChallengeStatusBy(practiceId: String)

    fun getErrorLogbookChallengeStatus(userId: String): ErrorLogbookChallengeStatus?

    fun disablePopUpOfChallengeStatus(
        userId: String,
        courseId: String,
    ): ErrorLogbookChallengeEventEntity?

    fun deleteChallengeStatus(
        userId: String,
        courseId: String,
    )

    fun refreshErrorLogbookChallengeStatusBy(
        cognitoUid: String,
        courseId: String,
    )

    fun saveFavoriteQuestion(
        practiceId: String,
        questionId: String,
        cognitoUid: String,
    ): ErrorLogbook

    fun getErrorLogbooksByPracticeId(
        practiceId: String,
        cognitoUid: String,
    ): List<ErrorLogbook>

    fun getUnresolvedErrorLogbookCountByCourse(cognitoUid: String): Map<String, Int>
}
