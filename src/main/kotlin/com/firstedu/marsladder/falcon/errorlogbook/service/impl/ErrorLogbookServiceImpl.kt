package com.firstedu.marsladder.falcon.errorlogbook.service.impl

import com.firstedu.marsladder.falcon.client.fuxi.FuxiClient
import com.firstedu.marsladder.falcon.course.repository.CourseOutlineItemDetailRepository
import com.firstedu.marsladder.falcon.course.repository.CourseOutlineItemRepository
import com.firstedu.marsladder.falcon.course.repository.entity.CourseOutlineItemEntity
import com.firstedu.marsladder.falcon.course.service.CourseService
import com.firstedu.marsladder.falcon.course.service.domain.Area
import com.firstedu.marsladder.falcon.course.service.domain.CourseOutline
import com.firstedu.marsladder.falcon.course.service.domain.Topic
import com.firstedu.marsladder.falcon.errorlogbook.ErrorLogbookConstants.DEFAULT_SORT_FIELD
import com.firstedu.marsladder.falcon.errorlogbook.ErrorLogbookEvent
import com.firstedu.marsladder.falcon.errorlogbook.ErrorLogbookEventType
import com.firstedu.marsladder.falcon.errorlogbook.ErrorLogbookStatus
import com.firstedu.marsladder.falcon.errorlogbook.repository.ErrorLogbookChallengeEventRepository
import com.firstedu.marsladder.falcon.errorlogbook.repository.ErrorLogbookDetailRepository
import com.firstedu.marsladder.falcon.errorlogbook.repository.ErrorLogbookRepository
import com.firstedu.marsladder.falcon.errorlogbook.repository.ErrorQuestionDetailRepository
import com.firstedu.marsladder.falcon.errorlogbook.repository.entity.ChallengeCourse
import com.firstedu.marsladder.falcon.errorlogbook.repository.entity.ErrorLogbookChallengeEventEntity
import com.firstedu.marsladder.falcon.errorlogbook.repository.entity.ErrorLogbookEntity
import com.firstedu.marsladder.falcon.errorlogbook.repository.entity.ErrorQuestionDetailEntity
import com.firstedu.marsladder.falcon.errorlogbook.repository.hasAreaIdEqual
import com.firstedu.marsladder.falcon.errorlogbook.repository.hasCognitoUIdEqual
import com.firstedu.marsladder.falcon.errorlogbook.repository.hasCourseIdEqual
import com.firstedu.marsladder.falcon.errorlogbook.repository.hasStatusEqual
import com.firstedu.marsladder.falcon.errorlogbook.repository.hasSubtopicIdsContain
import com.firstedu.marsladder.falcon.errorlogbook.repository.hasTopicIdsContain
import com.firstedu.marsladder.falcon.errorlogbook.repository.isFavorite
import com.firstedu.marsladder.falcon.errorlogbook.repository.isNotDeleted
import com.firstedu.marsladder.falcon.errorlogbook.service.ErrorLogbookService
import com.firstedu.marsladder.falcon.errorlogbook.service.domain.Content
import com.firstedu.marsladder.falcon.errorlogbook.service.domain.ErrorLogbook
import com.firstedu.marsladder.falcon.errorlogbook.service.domain.ErrorLogbookChallengeStatus
import com.firstedu.marsladder.falcon.errorlogbook.service.domain.ErrorLogbookOutlineTags
import com.firstedu.marsladder.falcon.errorlogbook.service.domain.SubTopic
import com.firstedu.marsladder.falcon.errorlogbook.service.exception.ErrorLogbookNotFoundException
import com.firstedu.marsladder.falcon.practice.PracticeResultStatus.SUBMITTED
import com.firstedu.marsladder.falcon.practice.repository.PracticeRepository
import com.firstedu.marsladder.falcon.practice.repository.entity.Answer
import com.firstedu.marsladder.falcon.practice.repository.entity.Player
import com.firstedu.marsladder.falcon.practice.repository.entity.PracticeEntity
import com.firstedu.marsladder.falcon.practice.repository.entity.Question
import com.firstedu.marsladder.falcon.practice.service.exception.NoPracticePermissionException
import com.firstedu.marsladder.falcon.practice.service.exception.PracticeNotFoundException
import com.firstedu.marsladder.falcon.client.fuxi.dto.PublishedQuestion
import com.firstedu.marsladder.falcon.question.service.exception.QuestionNotFoundException
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.domain.Specification.where
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
class ErrorLogbookServiceImpl(
    private val errorLogbookRepository: ErrorLogbookRepository,
    private val errorLogbookDetailRepository: ErrorLogbookDetailRepository,
    private val errorQuestionDetailRepository: ErrorQuestionDetailRepository,
    private val practiceRepository: PracticeRepository,
    private val courseOutlineItemRepository: CourseOutlineItemRepository,
    private val courseOutlineItemDetailRepository: CourseOutlineItemDetailRepository,
    private val fuxiClient: FuxiClient,
    private val courseService: CourseService,
    private val errorLogbookChallengeEventRepository: ErrorLogbookChallengeEventRepository,
    @Value("\${error.logbook.challenge.status.enter.count:50}")
    val errorLogbookChallengeStatusEnterCount: Int,
    @Value("\${error.logbook.challenge.status.exit.count:30}")
    val errorLogbookChallengeStatusExitCount: Int,
) : ErrorLogbookService {
    companion object {
        private val logger = LoggerFactory.getLogger(ErrorLogbookServiceImpl::class.java)
    }

    override fun getAll(
        cognitoUid: String,
        courseId: String?,
        areaId: String?,
        topicId: String?,
        subtopicId: String?,
        status: ErrorLogbookStatus?,
        favorite: Boolean?,
        errorCauses: List<String>?,
        sort: Sort.Direction?,
    ): List<ErrorLogbook> =
        errorLogbookDetailRepository
            .findAll(
                where(
                    hasCognitoUIdEqual(cognitoUid)
                        .and(isNotDeleted())
                        .and(hasCourseIdEqual(courseId))
                        .and(hasAreaIdEqual(areaId))
                        .and(hasTopicIdsContain(topicId))
                        .and(hasSubtopicIdsContain(subtopicId))
                        .and(hasStatusEqual(status))
                        .and(isFavorite(favorite)),
                ),
                Sort.by(sort ?: Sort.Direction.DESC, DEFAULT_SORT_FIELD),
            ).map { ErrorLogbook.from(it) }

    override fun getAllErrorLogbooks(
        cognitoUid: String,
        courseId: String?,
        areaId: String?,
        topicId: String?,
        subtopicId: String?,
        status: ErrorLogbookStatus?,
        favorite: Boolean?,
        errorCauses: List<String>?,
        pageable: Pageable,
    ): Page<ErrorLogbook> =
        errorLogbookDetailRepository
            .findAll(
                where(
                    hasCognitoUIdEqual(cognitoUid)
                        .and(isNotDeleted())
                        .and(hasCourseIdEqual(courseId))
                        .and(hasAreaIdEqual(areaId))
                        .and(hasTopicIdsContain(topicId))
                        .and(hasSubtopicIdsContain(subtopicId))
                        .and(hasStatusEqual(status))
                        .and(isFavorite(favorite)),
                ),
                pageable,
            ).map { ErrorLogbook.from(it) }

    @Transactional
    override fun savePracticeQuestionsForErrorLogbook(practiceId: String) =
        practiceRepository.findById(practiceId).ifPresent { practiceEntity ->
            practiceEntity.questions.forEach { question ->
                fuxiClient.findPublishedQuestionById(question.publishedQuestionId!!).ifPresent { publishedQuestionEntity ->
                    val subTopics = courseOutlineItemRepository.findAllById(publishedQuestionEntity.subTopics)
                    practiceEntity.players.filter { it.status == SUBMITTED }.forEach { player ->
                        player.answers.find { it.questionId == question.id }?.let { answer ->
                            answer.takeUnless { it.correct }?.let {
                                saveErrorQuestion(publishedQuestionEntity, player, question, practiceEntity, answer, subTopics)
                            }
                            savePracticeQuestion(publishedQuestionEntity, player, practiceEntity, answer, subTopics)
                        } ?: run {
                            saveErrorQuestion(publishedQuestionEntity, player, question, practiceEntity, buildUnansweredQuestion(question), subTopics)
                            savePracticeQuestion(publishedQuestionEntity, player, practiceEntity, buildUnansweredQuestion(question), subTopics)
                        }
                    }
                }
            }
        }

    private fun buildUnansweredQuestion(question: Question) = Answer(questionId = question.id, optionId = "", correct = false)

    override fun hasPermissionToReadOrUpdateErrorLogbook(
        errorLogbookId: String,
        userId: String,
    ): Boolean =
        errorLogbookRepository.findById(errorLogbookId).map { StringUtils.equals(it.cognitoUid, userId) }
            .orElse(false)

    override fun getOutlineTags(
        errorLogbookId: String,
        userId: String,
    ): ErrorLogbookOutlineTags {
        val errorLogbookEntity = getErrorLogbookEntity(userId, errorLogbookId)
        val contentTags = courseOutlineItemDetailRepository.findAllById(errorLogbookEntity.contentTags)
        val subTopicAndContentsMap = contentTags.groupBy { Pair(it.parentId, it.parentName) }
        return ErrorLogbookOutlineTags(
            subTopics =
                subTopicAndContentsMap.keys.map {
                    SubTopic(
                        id = it.first!!,
                        name = it.second!!,
                        contents =
                            subTopicAndContentsMap[it]!!.map { content ->
                                Content(
                                    id = content.id,
                                    name = content.name,
                                )
                            },
                    )
                },
        )
    }

    override fun updateErrorCauses(
        userId: String,
        errorLogbookId: String,
        errorCauseIds: List<String>,
    ) {
        val errorLogbookEntity = getErrorLogbookEntity(userId, errorLogbookId)
        errorLogbookEntity.errorCauses = errorCauseIds
        errorLogbookRepository.save(errorLogbookEntity)
    }

    override fun batchUpdateErrorCauses(
        userId: String,
        errorQuestionsIds: List<String>,
        errorCauseIds: List<String>,
    ) {
        val errorLogbookEntities = errorLogbookRepository.findByCognitoUidAndIdIn(userId, errorQuestionsIds)
        errorLogbookRepository.saveAll(
            errorLogbookEntities.map {
                it.errorCauses = errorCauseIds
                it
            },
        )
    }

    override fun getErrorLogbookCourseOutline(
        userId: String,
        courseId: String,
    ): CourseOutline {
        val courseOutlineMap = mutableMapOf<String, Map<String, Set<String>>>()
        errorLogbookRepository.findByCognitoUidAndCourseIdAndDeletedAndStatus(
            userId,
            courseId,
            false,
            ErrorLogbookStatus.UNRESOLVED,
        ).groupBy { it.areaId }.map { entry ->
            val subTopicIds = entry.value.flatMap { errorLogbookEntity -> errorLogbookEntity.subtopicIds }.toSet()
            val topicIds = entry.value.flatMap { errorLogbookEntity -> errorLogbookEntity.topicIds }.distinct()
            val courseTopicIds =
                courseOutlineItemRepository.findAllById(topicIds)
                    .map { item -> item.id!! to item.subItems.map { subItem -> subItem.id!! }.intersect(subTopicIds).toSet() }
                    .toMap()
            courseOutlineMap[entry.key] = courseTopicIds
        }
        val courseOutline = courseService.getActiveCourseOutline(courseId)
        return CourseOutline(
            courseOutlineId = courseOutline.courseOutlineId,
            areas =
                courseOutline.areas.map { area ->
                    Area(
                        area.id,
                        area.name,
                        area.iconUrl,
                        area.topics.map { topic ->
                            Topic(
                                topic.id,
                                topic.name,
                                topic.subTopics.filter { subTopic ->
                                    courseOutlineMap[area.id]?.get(topic.id)
                                        ?.contains(subTopic.id) ?: false
                                },
                            )
                        },
                    )
                },
        )
    }

    override fun favoriteErrorQuestion(
        userId: String,
        errorLogbookId: String,
    ) {
        val errorLogbookEntity = getErrorLogbookEntity(userId, errorLogbookId)
        errorLogbookEntity.favorite = true
        errorLogbookRepository.save(errorLogbookEntity)
    }

    override fun unfavoriteErrorQuestion(
        userId: String,
        errorLogbookId: String,
    ) {
        val errorLogbookEntity = getErrorLogbookEntity(userId, errorLogbookId)
        errorLogbookEntity.favorite = false
        errorLogbookRepository.save(errorLogbookEntity)
    }

    override fun deleteErrorQuestion(
        userId: String,
        errorLogbookId: String,
    ) {
        val errorLogbookEntity = getErrorLogbookEntity(userId, errorLogbookId)
        errorLogbookEntity.deleted = true
        errorLogbookRepository.save(errorLogbookEntity)
    }

    override fun deleteErrorQuestions(
        userId: String,
        errorQuestionsIds: List<String>,
    ) {
        val errorLogbookEntities = errorLogbookRepository.findByCognitoUidAndIdIn(userId, errorQuestionsIds)
        errorLogbookRepository.saveAll(
            errorLogbookEntities.map {
                it.deleted = true
                it
            },
        )
    }

    override fun getErrorLogbookChallengeStatus(userId: String) =
        errorLogbookChallengeEventRepository.findByCognitoUid(userId)?.let {
            ErrorLogbookChallengeStatus.from(it)
        }

    override fun disablePopUpOfChallengeStatus(
        userId: String,
        courseId: String,
    ) = errorLogbookChallengeEventRepository.findByCognitoUid(userId)?.let { errorLogbookChallengeEventEntity ->
        errorLogbookChallengeEventEntity.challengeCourses.find { it.courseId == courseId }?.let {
            errorLogbookChallengeEventRepository.save(
                errorLogbookChallengeEventEntity.apply {
                    it.shouldPopUp = false
                },
            )
        }
    }

    override fun deleteChallengeStatus(
        userId: String,
        courseId: String,
    ) {
        errorLogbookChallengeEventRepository.findByCognitoUid(userId)?.let { errorLogbookChallengeEventEntity ->
            errorLogbookChallengeEventEntity.challengeCourses.find { it.courseId == courseId }?.let {
                errorLogbookChallengeEventRepository.delete(errorLogbookChallengeEventEntity)
            }
        }
    }

    override fun saveFavoriteQuestion(
        practiceId: String,
        questionId: String,
        cognitoUid: String,
    ): ErrorLogbook {
        val existingErrorLogbookQuestionOptional = errorLogbookRepository.findByCognitoUidAndQuestionId(cognitoUid, questionId)
        if (existingErrorLogbookQuestionOptional.isPresent) {
            val existingErrorLogbookEntity = existingErrorLogbookQuestionOptional.get()
            val event =
                ErrorLogbookEvent(
                    type = ErrorLogbookEventType.UNRESOLVE,
                    message = "Deleted error question unresolved, practiceId: $practiceId",
                    saveAt = LocalDateTime.now().toString(),
                )
            val updatedErrorLogbookEntity = reviveErrorLogbookEntity(existingErrorLogbookEntity, event)
            return ErrorLogbook.from(updatedErrorLogbookEntity)
        }

        val practiceEntity =
            practiceRepository.findById(practiceId).orElseThrow {
                PracticeNotFoundException("Practice not found. Id: $practiceId")
            }

        val question = practiceEntity.questions.first { it.id == questionId }

        var answer =
            Answer(
                questionId = questionId,
                optionId = "",
                correct = false,
            )

        try {
            answer =
                practiceEntity.players.first { it.cognitoUid == cognitoUid }.answers.first { it.questionId == questionId }
        } catch (e: Exception) {
            logger.warn("Practice doesn't have enough answers, use the default answer. Practice Id: $practiceId, Question Id: $questionId")
        }

        val publishedQuestionEntity =
            fuxiClient.findPublishedQuestionById(question.publishedQuestionId!!).orElseThrow {
                QuestionNotFoundException("Question not found. Id: $questionId")
            }

        val subTopics = courseOutlineItemRepository.findAllById(publishedQuestionEntity.subTopics)

        val errorLogbookEntity =
            errorLogbookRepository.save(
                ErrorLogbookEntity(
                    cognitoUid = cognitoUid,
                    courseId = practiceEntity.courseId,
                    areaId = practiceEntity.metadata.areaId,
                    areaIds = subTopics.mapNotNull { it.parentItem }.mapNotNull { it.parentId }.distinct(),
                    topicIds = subTopics.mapNotNull { it.parentId }.distinct(),
                    subtopicIds = publishedQuestionEntity.subTopics,
                    question = question,
                    optionId = answer.optionId,
                    status = if (answer.correct) ErrorLogbookStatus.RESOLVED else ErrorLogbookStatus.UNRESOLVED,
                    errorCauses = emptyList(),
                    favorite = false,
                    deleted = false,
                    difficulty = question.difficulty,
                    questionId = question.id,
                    practiceId = practiceEntity.id!!,
                    contentTags = publishedQuestionEntity.contents,
                    events =
                        listOf(
                            ErrorLogbookEvent(
                                type = ErrorLogbookEventType.FAVORITE,
                                message = "Favorite question, practiceId: ${practiceEntity.id}",
                                saveAt = LocalDateTime.now().toString(),
                            ),
                        ),
                ),
            )
        return ErrorLogbook.from(errorLogbookEntity)
    }

    override fun getErrorLogbooksByPracticeId(
        practiceId: String,
        cognitoUid: String,
    ): List<ErrorLogbook> {
        val practiceEntity =
            practiceRepository.findById(practiceId).orElseThrow {
                PracticeNotFoundException("Practice not found. Id: $practiceId")
            }

        practiceEntity.players.find { it.cognitoUid == cognitoUid }
            ?: throw NoPracticePermissionException("No permission to access practice. Id: $practiceId")

        val questionIds = practiceEntity.questions.map { it.id }
        val errorLogbookEntities = errorLogbookRepository.findByCognitoUidAndQuestionIdIn(cognitoUid, questionIds)
        return errorLogbookEntities.map { ErrorLogbook.from(it) }
    }

    override fun getUnresolvedErrorLogbookCountByCourse(cognitoUid: String): Map<String, Int> {
        return errorLogbookRepository.countUnresolvedErrorLogbooksByCourse(cognitoUid)
            .associate { it.getCourseId() to it.getUnresolvedCount() }
    }

    @Transactional
    override fun refreshErrorLogbookChallengeStatusBy(practiceId: String) =
        practiceRepository.findById(practiceId).ifPresent { practiceEntity ->
            practiceEntity.players.forEach { player ->
                saveOrUpdateErrorLogbookChallengeEvent(player.cognitoUid, practiceEntity.courseId)
            }
        }

    @Transactional
    override fun refreshErrorLogbookChallengeStatusBy(
        cognitoUid: String,
        courseId: String,
    ) {
        saveOrUpdateErrorLogbookChallengeEvent(cognitoUid, courseId)
    }

    private fun saveOrUpdateErrorLogbookChallengeEvent(
        cognitoUid: String,
        courseId: String,
    ) {
        val totalErrorLogbookCountOfCourse =
            errorLogbookRepository.findByCognitoUidAndCourseIdAndDeletedAndStatus(
                cognitoUid = cognitoUid,
                courseId = courseId,
                deleted = false,
                status = ErrorLogbookStatus.UNRESOLVED,
            ).size

        val errorLogbookChallengeEventEntity = errorLogbookChallengeEventRepository.findByCognitoUid(cognitoUid)

        val challengeEvent =
            updateCourseChallengeStatus(
                totalErrorLogbookCountOfCourse,
                errorLogbookChallengeEventEntity,
                courseId,
                cognitoUid,
            )

        challengeEvent?.let {
            errorLogbookChallengeEventRepository.save(it)
        }
    }

    private fun updateCourseChallengeStatus(
        totalErrorLogbookCountOfCourse: Int,
        errorLogbookChallengeEventEntity: ErrorLogbookChallengeEventEntity?,
        courseId: String,
        cognitoUid: String,
    ) = when {
        totalErrorLogbookCountOfCourse >= errorLogbookChallengeStatusEnterCount ->
            errorLogbookChallengeEventEntity.updateEnterChallengeStatus(
                cognitoUid,
                courseId,
            )

        totalErrorLogbookCountOfCourse < errorLogbookChallengeStatusExitCount ->
            errorLogbookChallengeEventEntity?.updateExitChallengeStatus(
                courseId,
            )

        else -> null
    }

    private fun ErrorLogbookChallengeEventEntity?.updateEnterChallengeStatus(
        cognitoUid: String,
        courseId: String,
    ) = when {
        this == null -> buildEnterChallengeStatusEvent(cognitoUid, courseId)
        challengeCourses.find { it.courseId == courseId } == null ->
            apply {
                challengeCourses.add(ChallengeCourse(courseId, true))
            }

        else -> null
    }

    private fun ErrorLogbookChallengeEventEntity.updateExitChallengeStatus(courseId: String) =
        challengeCourses.find { it.courseId == courseId }?.let {
            challengeCourses.remove(it)
            this
        }

    private fun buildEnterChallengeStatusEvent(
        cognitoUid: String,
        courseId: String,
    ) = ErrorLogbookChallengeEventEntity(
        cognitoUid = cognitoUid,
        challengeCourses = mutableSetOf(ChallengeCourse(courseId, true)),
    )

    private fun getErrorLogbookEntity(
        cognitoUid: String,
        errorLogbookId: String,
    ) = errorLogbookRepository.findByCognitoUidAndId(cognitoUid, errorLogbookId)
        ?: throw ErrorLogbookNotFoundException("Error logbook not found, ID: $errorLogbookId")

    private fun saveErrorQuestion(
        publishedQuestion: PublishedQuestion,
        player: Player,
        question: Question,
        practiceEntity: PracticeEntity,
        answer: Answer,
        subTopics: List<CourseOutlineItemEntity>,
    ) {
        errorLogbookRepository.findByCognitoUidAndQuestionId(
            player.cognitoUid,
            question.id,
        ).ifPresentOrElse(
            { existErrorLogbookEntity ->
                existErrorLogbookEntity.takeIf { it.deleted || it.status == ErrorLogbookStatus.RESOLVED }
                    ?.let {
                        reviveErrorLogbookEntity(
                            it,
                            ErrorLogbookEvent(
                                type = ErrorLogbookEventType.UNRESOLVE,
                                message = "Error question unresolved, practiceId: ${practiceEntity.id!!}",
                                saveAt = LocalDateTime.now().toString(),
                            ),
                        )
                    }
            },
            {
                errorLogbookRepository.save(
                    ErrorLogbookEntity(
                        cognitoUid = player.cognitoUid,
                        courseId = practiceEntity.courseId,
                        areaId = practiceEntity.metadata.areaId,
                        areaIds = subTopics.mapNotNull { it.parentItem }.mapNotNull { it.parentId }.distinct(),
                        topicIds = subTopics.mapNotNull { it.parentId }.distinct(),
                        subtopicIds = publishedQuestion.subTopics,
                        question = question,
                        optionId = answer.optionId,
                        status = ErrorLogbookStatus.UNRESOLVED,
                        errorCauses = emptyList(),
                        favorite = false,
                        deleted = false,
                        difficulty = question.difficulty,
                        questionId = question.id,
                        practiceId = practiceEntity.id!!,
                        contentTags = publishedQuestion.contents,
                        events =
                            listOf(
                                ErrorLogbookEvent(
                                    type = ErrorLogbookEventType.ADD,
                                    message = "Add into error logbook, practiceId: ${practiceEntity.id}",
                                    saveAt = LocalDateTime.now().toString(),
                                ),
                            ),
                    ),
                )
            },
        )
    }

    private fun reviveErrorLogbookEntity(
        existingErrorLogbookEntity: ErrorLogbookEntity,
        errorLogbookEvent: ErrorLogbookEvent,
    ): ErrorLogbookEntity {
        val appendedEvents = existingErrorLogbookEntity.events.toMutableList()
        appendedEvents.add(errorLogbookEvent)

        val updatedEntity =
            existingErrorLogbookEntity.apply {
                deleted = false
                status = ErrorLogbookStatus.UNRESOLVED
                events = appendedEvents
            }
        return errorLogbookRepository.save(updatedEntity)
    }

    private fun savePracticeQuestion(
        publishedQuestion: PublishedQuestion,
        player: Player,
        practiceEntity: PracticeEntity,
        answer: Answer,
        subTopics: List<CourseOutlineItemEntity>,
    ) {
        if (errorQuestionDetailRepository.findByCognitoUidAndQuestionIdAndPracticeId(player.cognitoUid, publishedQuestion.originalQuestionId, practiceEntity.id!!).isPresent) {
            logger.warn("Current error question detail already exists, practiceId: $practiceEntity.id!!, questionId: $publishedQuestion.originalQuestionId")
            return
        }
        errorQuestionDetailRepository.save(
            ErrorQuestionDetailEntity(
                questionId = publishedQuestion.originalQuestionId,
                cognitoUid = player.cognitoUid,
                courseId = publishedQuestion.courseId,
                areaId = practiceEntity.metadata.areaId,
                topicIds = subTopics.mapNotNull { it.parentId }.distinct(),
                practiceId = practiceEntity.id!!,
                correct = answer.correct,
                areaIds = subTopics.mapNotNull { it.parentItem }.mapNotNull { it.parentId }.distinct(),
                difficulty = publishedQuestion.difficulty,
                subtopicIds = subTopics.map { it.id!! }.distinct(),
                contentTags = publishedQuestion.contents,
            ),
        )
    }
}
