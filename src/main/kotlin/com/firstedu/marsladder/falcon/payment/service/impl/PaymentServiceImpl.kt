package com.firstedu.marsladder.falcon.payment.service.impl

import com.firstedu.marsladder.falcon.game.service.PlayerLadderService
import com.firstedu.marsladder.falcon.infrastructure.stripe.StripeClient
import com.firstedu.marsladder.falcon.infrastructure.stripe.StripePaymentResult
import com.firstedu.marsladder.falcon.payment.service.PaymentService
import com.firstedu.marsladder.falcon.payment.service.domain.StripePayment
import com.firstedu.marsladder.falcon.payment.service.domain.StripePaymentMethod
import com.firstedu.marsladder.falcon.payment.service.domain.StripeTeacherPayment
import com.firstedu.marsladder.falcon.payment.service.domain.SubscriptionWithSource
import com.firstedu.marsladder.falcon.payment.service.exception.PaymentNotFoundException
import com.firstedu.marsladder.falcon.payment.service.exception.StripePaymentException
import com.firstedu.marsladder.falcon.payment.type.SubscriptionItemSource
import com.firstedu.marsladder.falcon.seatSubscription.repository.SeatSubscriptionRepository
import com.firstedu.marsladder.falcon.seatSubscription.repository.SubscribedSeatRepository
import com.firstedu.marsladder.falcon.seatSubscription.repository.entity.SubscribedSeatEntity
import com.firstedu.marsladder.falcon.seatSubscription.service.exception.SubscribedSeatNotFoundException
import com.firstedu.marsladder.falcon.subscription.repository.CustomerRepository
import com.firstedu.marsladder.falcon.subscription.repository.SubscriptionRepository
import com.firstedu.marsladder.falcon.subscription.repository.entity.UpdateEventEntity
import com.firstedu.marsladder.falcon.subscription.service.exception.CustomerNotFoundException
import com.firstedu.marsladder.falcon.subscription.service.exception.InvalidSubscriptionOperationException
import com.firstedu.marsladder.falcon.subscription.service.exception.StripeCustomerNotFoundException
import com.firstedu.marsladder.falcon.subscription.service.exception.SubscriptionNotFoundException
import com.firstedu.marsladder.falcon.subscription.type.SubscriptionFrequency
import com.firstedu.marsladder.falcon.subscription.type.SubscriptionStatus
import com.firstedu.marsladder.falcon.subscription.type.UpdateEventType
import com.firstedu.marsladder.falcon.teachersubscription.repository.TeacherSubscriptionRepository
import com.stripe.Stripe
import com.stripe.exception.StripeException
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.env.Environment
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import javax.naming.NoPermissionException

@Service
class PaymentServiceImpl(
    private val stripeClient: StripeClient,
    private val customerRepository: CustomerRepository,
    private val subscriptionRepository: SubscriptionRepository,
    private val seatSubscriptionRepository: SeatSubscriptionRepository,
    private val subscribedSeatRepository: SubscribedSeatRepository,
    private val teacherSubscriptionRepository: TeacherSubscriptionRepository,
    private val environment: Environment,
) : PaymentService {
    companion object {
        private val logger = LoggerFactory.getLogger(PlayerLadderService::class.java)
        const val TRY_FOR_FREE_TEXT = "TRY_FOR_FREE"
        private const val PROD_ENVIRONMENT_PROFILE = "prod"
    }

    // TODO: inject stripe key only once
    @Value("\${subscription.stripe.secret}")
    private val stripSecretKey: String = ""

    override fun getPayments(
        cognitoUid: String,
        source: SubscriptionItemSource,
        currency: String,
    ): List<StripePayment> {
        val existedCustomer = customerRepository.findByCognitoUidAndCurrency(cognitoUid, currency)
        if (existedCustomer.isEmpty) return emptyList()
        val customer = existedCustomer.get()
        if (customer.customerId == TRY_FOR_FREE_TEXT) return emptyList()
        try {
            Stripe.apiKey = stripSecretKey
            return stripeClient.getPayments(customer.customerId)
                .mapNotNull { getPaymentDetails(cognitoUid, it) }
                .filter { it.source == source }
        } catch (e: StripeException) {
            throw StripePaymentException(e.message ?: "Get Payments failed!")
        }
    }

    override fun getTeacherPayments(
        cognitoUid: String,
        currency: String,
    ): List<StripeTeacherPayment> {
        val customerOptional = customerRepository.findByCognitoUidAndCurrency(cognitoUid, currency)
        if (customerOptional.isEmpty) return emptyList()

        val customer = customerOptional.get()
        if (customer.customerId == TRY_FOR_FREE_TEXT) return emptyList()

        return try {
            Stripe.apiKey = stripSecretKey
            stripeClient.getPayments(customer.customerId)
                .mapNotNull { getTeacherPaymentDetails(cognitoUid, it) }
        } catch (e: StripeException) {
            throw StripePaymentException(e.message ?: "Get Payments failed!")
        }
    }

    override fun getPayment(
        cognitoUid: String,
        paymentId: String,
    ): StripePayment {
        val existedCustomers = customerRepository.findByCognitoUid(cognitoUid)
        if (existedCustomers.isEmpty()) {
            throw StripeCustomerNotFoundException("Customer not found!")
        }
        try {
            Stripe.apiKey = stripSecretKey
            // TODO (MAR-1201): validate payment is related to customer
            val payment = stripeClient.getPayment(paymentId)
            return getPaymentDetails(cognitoUid, payment) ?: throw PaymentNotFoundException("Payment $paymentId not found.")
        } catch (e: StripeException) {
            throw StripePaymentException(e.message ?: "Get Payments failed!")
        }
    }

    override fun updatePaymentMethod(
        cognitoUid: String,
        subscriptionId: String,
        paymentMethodId: String,
    ) {
        val subscriptionEntity = findSubscriptionByCognitoUidAndId(cognitoUid, subscriptionId)
        if (subscriptionEntity.stripeSubscriptionId == TRY_FOR_FREE_TEXT) {
            throw NoPermissionException("It's free account, you can't update payment.")
        }

        if (subscriptionEntity.status != SubscriptionStatus.SUCCESS) {
            throw InvalidSubscriptionOperationException("Cannot update payment method for subscription: $subscriptionId")
        }
        try {
            Stripe.apiKey = stripSecretKey
            val subscriptionResult = stripeClient.getSubscription(subscriptionEntity.stripeSubscriptionId!!)
            val customer =
                customerRepository.findByCognitoUidAndCurrency(cognitoUid, subscriptionResult.currency).orElseThrow {
                    throw CustomerNotFoundException("Customer not found. Cognito UID: $cognitoUid")
                }

            stripeClient.attachPaymentMethod(paymentMethodId, customer.customerId)
            stripeClient.updatePaymentMethod(paymentMethodId, subscriptionEntity.stripeSubscriptionId!!)

            val events = (subscriptionEntity.events ?: emptyList()).toMutableList()
            events.add(
                UpdateEventEntity(
                    updatedAt = LocalDateTime.now(),
                    type = UpdateEventType.UPDATE_PAYMENT_METHOD,
                    subscribedCourseId = null,
                ),
            )
            subscriptionEntity.events = events
            subscriptionRepository.save(subscriptionEntity)
        } catch (e: StripeException) {
            throw StripePaymentException(e.message ?: "Update Payment Method failed!")
        }
    }

    override fun getPaymentMethods(cognitoUid: String): List<StripePaymentMethod> {
        val customers = findCustomersByCognitoUid(cognitoUid)
        try {
            Stripe.apiKey = stripSecretKey
            return customers.map { stripeClient.getPaymentMethods(it.customerId).map(StripePaymentMethod.Companion::from) }.flatten()
        } catch (e: StripeException) {
            throw StripePaymentException(e.message ?: "Get Payment Methods failed!")
        }
    }

    private fun getTeacherPaymentDetails(
        cognitoUid: String,
        payment: StripePaymentResult,
    ): StripeTeacherPayment? {
        val teacherSubscription =
            teacherSubscriptionRepository.findByCognitoUidAndStripeSubscriptionId(cognitoUid, payment.invoice.subscriptionId)
                .orElse(null) ?: run {
                logger.warn(
                    "No teacher subscription found for CognitoUid= $cognitoUid and SubscriptionId: ${payment.invoice.subscriptionId}",
                )
                return null
            }

        val paymentMethodResult = stripeClient.getPaymentMethod(payment.paymentMethod)
        val stripePaymentResult = updatePaymentResult(payment, teacherSubscription.id!!)

        val nextPaymentTimeForPeriod =
            getNextBillingTimeForPeriod(
                teacherSubscription.startAt!!,
                stripePaymentResult.created,
                teacherSubscription.frequency!!,
            )

        return StripeTeacherPayment.from(
            stripePaymentResult,
            paymentMethodResult,
            nextPaymentTimeForPeriod,
            teacherSubscription.frequency!!,
        )
    }

    private fun getPaymentDetails(
        cognitoUid: String,
        payment: StripePaymentResult,
    ): StripePayment? {
        val subscriptionWithSource = findSubscriptionByCognitoUidAndStripeSubscriptionId(cognitoUid, payment.invoice.subscriptionId) ?: return null
        val paymentMethodResult = stripeClient.getPaymentMethod(payment.paymentMethod)
        val stripePaymentResult = updatePaymentResult(payment, subscriptionWithSource.subscriptionId)
        val subscribedSeatEntity =
            if (subscriptionWithSource.source == SubscriptionItemSource.SEAT) {
                findSubscribedSeatEntity(
                    subscriptionWithSource.subscriptionId,
                )
            } else {
                null
            }
        val seatHistoryEntity =
            if (subscriptionWithSource.source == SubscriptionItemSource.SEAT) {
                getSeatHistoryForPeriod(
                    subscribedSeatEntity!!,
                    payment.created,
                )
            } else {
                null
            }
        val nextPaymentTimeForPeriod =
            getNextBillingTimeForPeriod(
                subscriptionWithSource.startAt,
                stripePaymentResult.created,
                seatHistoryEntity?.frequency ?: subscriptionWithSource.frequency,
            )
        return StripePayment.from(
            stripePaymentResult,
            paymentMethodResult,
            subscriptionWithSource.source,
            nextPaymentTimeForPeriod,
            seatHistoryEntity?.seats ?: subscribedSeatEntity?.seats,
            seatHistoryEntity?.increasedSeats ?: 0,
        )
    }

    private fun getMonthsForFrequency(frequency: SubscriptionFrequency) =
        when (frequency) {
            SubscriptionFrequency.MONTHLY -> 1
            SubscriptionFrequency.HALF_YEARLY -> 6
            SubscriptionFrequency.YEARLY -> 12
        }.toLong()

    private fun getNextBillingTimeForPeriod(
        startAt: LocalDateTime,
        paymentCreated: LocalDateTime,
        frequency: SubscriptionFrequency,
    ): LocalDateTime {
        for (i in 1..120) {
            val startPaymentTime = startAt.plusMonths((i - 1) * getMonthsForFrequency(frequency))
            val nextPaymentTime = startAt.plusMonths(i * getMonthsForFrequency(frequency))
            if (!startPaymentTime.isAfter(paymentCreated) && nextPaymentTime.isAfter(paymentCreated)) {
                return nextPaymentTime
            }
        }
        throw PaymentNotFoundException("No payment period found.")
    }

    private fun getSeatHistoryForPeriod(
        subscribedSeatEntity: SubscribedSeatEntity,
        paymentCreated: LocalDateTime,
    ) = subscribedSeatEntity.seatsHistory?.firstOrNull { h -> h.createdAt.isAfter(paymentCreated) }

    private fun findSubscribedSeatEntity(subscriptionId: String) =
        subscribedSeatRepository.findBySubscriptionId(subscriptionId)
            ?: throw SubscribedSeatNotFoundException("Subscribed seat not found for subscription. ID: $subscriptionId")

    private fun updatePaymentResult(
        payment: StripePaymentResult,
        subscriptionId: String,
    ): StripePaymentResult {
        val invoice = payment.invoice.copy(subscriptionId = subscriptionId)
        return payment.copy(invoice = invoice)
    }

    private fun findSubscriptionByCognitoUidAndStripeSubscriptionId(
        cognitoUid: String,
        stripeSubscriptionId: String,
    ): SubscriptionWithSource? =
        subscriptionRepository.findByCognitoUidAndStripeSubscriptionId(cognitoUid, stripeSubscriptionId)
            .map { SubscriptionWithSource(it.id!!, it.startAt!!, it.frequency!!, SubscriptionItemSource.COURSE) }
            .orElseGet {
                val subscription = seatSubscriptionRepository.findByCognitoUidAndStripeSubscriptionId(cognitoUid, stripeSubscriptionId)
                if (!environment.activeProfiles.contains(PROD_ENVIRONMENT_PROFILE) && subscription == null) {
                    null
                } else if (subscription == null) {
                    throw SubscriptionNotFoundException("(Seat) Subscription not found. Stripe ID: $stripeSubscriptionId")
                } else {
                    SubscriptionWithSource(subscription.id!!, subscription.startAt!!, subscription.frequency!!, SubscriptionItemSource.SEAT)
                }
            }

    private fun findSubscriptionByCognitoUidAndId(
        cognitoUid: String,
        subscriptionId: String,
    ) = subscriptionRepository.findByCognitoUidAndId(cognitoUid, subscriptionId)
        .orElseThrow { SubscriptionNotFoundException("Subscription not found. ID: $subscriptionId") }

    private fun findCustomersByCognitoUid(cognitoUid: String) = customerRepository.findByCognitoUid(cognitoUid)
}
