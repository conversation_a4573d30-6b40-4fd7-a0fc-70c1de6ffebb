package com.firstedu.marsladder.falcon.errorlogbook.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.firstedu.marsladder.falcon.course.service.CourseService
import com.firstedu.marsladder.falcon.course.service.domain.Area
import com.firstedu.marsladder.falcon.course.service.domain.Course
import com.firstedu.marsladder.falcon.course.service.domain.CourseOutline
import com.firstedu.marsladder.falcon.errorlogbook.ErrorLogbookStatus
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.BatchUpdateErrorCausesRequest
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.DeleteErrorQuestionsRequest
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.PracticeFromErrorLogbookRequest
import com.firstedu.marsladder.falcon.errorlogbook.controller.dto.UpdateErrorCausesRequest
import com.firstedu.marsladder.falcon.errorlogbook.service.ErrorLogbookPracticeService
import com.firstedu.marsladder.falcon.errorlogbook.service.ErrorLogbookService
import com.firstedu.marsladder.falcon.errorlogbook.service.domain.CourseChallengeStatus
import com.firstedu.marsladder.falcon.errorlogbook.service.domain.ErrorLogbook
import com.firstedu.marsladder.falcon.errorlogbook.service.domain.ErrorLogbookChallengeStatus
import com.firstedu.marsladder.falcon.errorlogbook.service.exception.ErrorLogbookNotFoundException
import com.firstedu.marsladder.falcon.practice.CourseTopicId
import com.firstedu.marsladder.falcon.security.FalconJwtAuthenticationTokenConverter
import com.firstedu.marsladder.falcon.security.SessionProvider
import com.firstedu.marsladder.falcon.security.WebSecurityConfig
import com.firstedu.marsladder.falcon.subscription.service.SubscribedCourseService
import com.firstedu.marsladder.falcon.user.WebMvcTestConfiguration
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.doNothing
import com.nhaarman.mockitokotlin2.doReturn
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.FilterType
import org.springframework.context.annotation.Import
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(
    controllers = [ErrorLogbookController::class],
    excludeFilters = [
        ComponentScan.Filter(
            type = FilterType.ASSIGNABLE_TYPE,
            classes = [
                FalconJwtAuthenticationTokenConverter::class,
            ],
        ),
    ],
)
@Import(WebSecurityConfig::class, WebMvcTestConfiguration::class)
internal class ErrorLogbookControllerTest
    @Autowired
    private constructor(
        private val mockMvc: MockMvc,
        private val objectMapper: ObjectMapper,
    ) {
        @MockBean
        private lateinit var errorLogbookService: ErrorLogbookService

        @MockBean
        private lateinit var errorLogbookPracticeService: ErrorLogbookPracticeService

        @MockBean
        private lateinit var courseService: CourseService

        @MockBean
        private lateinit var sessionProvider: SessionProvider

        @MockBean
        private lateinit var subscribedCourseService: SubscribedCourseService

        private val fakeErrorLogbookId = "fakeErrorLogbookId"
        private val fakeUserId = "fakeUserCognitoUid"

        @BeforeEach
        internal fun setUp() {
            whenever(sessionProvider.getUserId()).thenReturn(fakeUserId)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return practice id when creating error related practice`() {
            val fakePracticeId = "fakePracticeId"
            whenever(errorLogbookService.hasPermissionToReadOrUpdateErrorLogbook(fakeErrorLogbookId, fakeUserId)).thenReturn(true)
            whenever(
                errorLogbookPracticeService.createErrorRelatedPractice(eq(fakeUserId), eq(fakeErrorLogbookId)),
            ).thenReturn(fakePracticeId)

            mockMvc
                .perform(
                    post("/error-logbooks/$fakeErrorLogbookId/practices")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isOk)
                .andExpect(content().string(fakePracticeId))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 403 when creating error related practice auth failed`() {
            whenever(errorLogbookService.hasPermissionToReadOrUpdateErrorLogbook(fakeErrorLogbookId, fakeUserId)).thenReturn(false)

            mockMvc
                .perform(
                    post("/error-logbooks/$fakeErrorLogbookId/practices")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isForbidden)
                .andExpect(jsonPath("$.message[0]").value("Permission deny!"))

            verify(errorLogbookPracticeService, times(0)).createErrorRelatedPractice(eq(fakeUserId), eq(fakeErrorLogbookId))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 204 when update error causes for error logbook question`() {
            val updateErrorCausesRequest = UpdateErrorCausesRequest(errorCauseIds = listOf("cause-1"))
            doNothing().whenever(
                errorLogbookService,
            ).updateErrorCauses(fakeUserId, fakeErrorLogbookId, updateErrorCausesRequest.errorCauseIds)

            mockMvc
                .perform(
                    put("/error-logbooks/$fakeErrorLogbookId/error-causes")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateErrorCausesRequest)),
                )
                .andExpect(status().isNoContent)

            verify(errorLogbookService, times(1)).updateErrorCauses(eq(fakeUserId), eq(fakeErrorLogbookId), any())
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 404 when update non-existing error logbook question`() {
            val updateErrorCausesRequest = UpdateErrorCausesRequest(errorCauseIds = listOf("cause-1"))
            whenever(
                errorLogbookService.updateErrorCauses(fakeUserId, fakeErrorLogbookId, updateErrorCausesRequest.errorCauseIds),
            ).thenThrow(ErrorLogbookNotFoundException("error logbook not found."))

            mockMvc
                .perform(
                    put("/error-logbooks/$fakeErrorLogbookId/error-causes")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateErrorCausesRequest)),
                )
                .andExpect(status().isNotFound)

            verify(errorLogbookService, times(1)).updateErrorCauses(eq(fakeUserId), eq(fakeErrorLogbookId), any())
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 400 when update error causes without request body for error logbook question`() {
            mockMvc
                .perform(put("/error-logbooks/$fakeErrorLogbookId/error-causes").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 204 when delete error questions`() {
            val deleteErrorQuestionsRequest = DeleteErrorQuestionsRequest(errorQuestionsIds = listOf("error-question-id"))
            doNothing().whenever(errorLogbookService).deleteErrorQuestions(fakeUserId, deleteErrorQuestionsRequest.errorQuestionsIds)

            mockMvc
                .perform(
                    delete("/error-logbooks")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(deleteErrorQuestionsRequest)),
                )
                .andExpect(status().isNoContent)

            verify(errorLogbookService, times(1)).deleteErrorQuestions(eq(fakeUserId), eq(deleteErrorQuestionsRequest.errorQuestionsIds))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 400 when delete error questions without request body`() {
            mockMvc
                .perform(delete("/error-logbooks").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 204 when update error causes`() {
            val batchUpdateErrorCausesRequest =
                BatchUpdateErrorCausesRequest(errorQuestionsIds = listOf("error-question-id"), errorCauseIds = listOf("error-cause-id"))
            doNothing().whenever(
                errorLogbookService,
            ).batchUpdateErrorCauses(
                fakeUserId,
                batchUpdateErrorCausesRequest.errorQuestionsIds,
                batchUpdateErrorCausesRequest.errorCauseIds,
            )

            mockMvc
                .perform(
                    put("/error-logbooks/error-causes")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(batchUpdateErrorCausesRequest)),
                )
                .andExpect(status().isNoContent)

            verify(
                errorLogbookService,
                times(1),
            ).batchUpdateErrorCauses(
                eq(fakeUserId),
                eq(batchUpdateErrorCausesRequest.errorQuestionsIds),
                eq(batchUpdateErrorCausesRequest.errorCauseIds),
            )
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 400 when update error causes without request body`() {
            mockMvc
                .perform(put("/error-logbooks/error-causes").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest)
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 204 when favorite error logbook question`() {
            doNothing().whenever(errorLogbookService).favoriteErrorQuestion(fakeUserId, fakeErrorLogbookId)

            mockMvc
                .perform(
                    post("/error-logbooks/$fakeErrorLogbookId/favorite")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isNoContent)

            verify(errorLogbookService, times(1)).favoriteErrorQuestion(eq(fakeUserId), eq(fakeErrorLogbookId))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 404 when favorite non-existing error logbook question`() {
            whenever(
                errorLogbookService.favoriteErrorQuestion(fakeUserId, fakeErrorLogbookId),
            ).thenThrow(ErrorLogbookNotFoundException("error logbook not found."))

            mockMvc
                .perform(
                    post("/error-logbooks/$fakeErrorLogbookId/favorite")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isNotFound)

            verify(errorLogbookService, times(1)).favoriteErrorQuestion(eq(fakeUserId), eq(fakeErrorLogbookId))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 204 when unfavorite error logbook question`() {
            doNothing().whenever(errorLogbookService).unfavoriteErrorQuestion(fakeUserId, fakeErrorLogbookId)

            mockMvc
                .perform(
                    delete("/error-logbooks/$fakeErrorLogbookId/favorite")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isNoContent)

            verify(errorLogbookService, times(1)).unfavoriteErrorQuestion(eq(fakeUserId), eq(fakeErrorLogbookId))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 404 when unfavorite non-existing error logbook question`() {
            whenever(
                errorLogbookService.unfavoriteErrorQuestion(fakeUserId, fakeErrorLogbookId),
            ).thenThrow(ErrorLogbookNotFoundException("error logbook not found."))

            mockMvc
                .perform(
                    delete("/error-logbooks/$fakeErrorLogbookId/favorite")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isNotFound)

            verify(errorLogbookService, times(1)).unfavoriteErrorQuestion(eq(fakeUserId), eq(fakeErrorLogbookId))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 204 when delete error logbook question`() {
            doNothing().whenever(errorLogbookService).deleteErrorQuestion(fakeUserId, fakeErrorLogbookId)

            mockMvc
                .perform(
                    delete("/error-logbooks/$fakeErrorLogbookId")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isNoContent)

            verify(errorLogbookService, times(1)).deleteErrorQuestion(eq(fakeUserId), eq(fakeErrorLogbookId))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return 404 when delete non-existing error logbook question`() {
            whenever(
                errorLogbookService.deleteErrorQuestion(fakeUserId, fakeErrorLogbookId),
            ).thenThrow(ErrorLogbookNotFoundException("error logbook not found."))

            mockMvc
                .perform(
                    delete("/error-logbooks/$fakeErrorLogbookId")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isNotFound)

            verify(errorLogbookService, times(1)).deleteErrorQuestion(eq(fakeUserId), eq(fakeErrorLogbookId))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return practice id when practice from error logbook`() {
            val fakePracticeId = "fakePracticeId"
            val request =
                PracticeFromErrorLogbookRequest(
                    "fakeCourseId",
                    false,
                    "fakeAreaId",
                    listOf(CourseTopicId("fakeTopicId", listOf("fakeSubTopicId"))),
                    false,
                )
            whenever(errorLogbookPracticeService.practiceFromErrorLogbook(eq(fakeUserId), eq(request))).thenReturn(fakePracticeId)

            mockMvc
                .perform(
                    post("/error-logbooks/practices")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)),
                )
                .andExpect(status().isOk)
                .andExpect(content().string(fakePracticeId))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return practice id when creating error challenge practice`() {
            val fakePracticeId = "fakePracticeId"
            whenever(errorLogbookPracticeService.challengeErrorLogbook(eq(fakeUserId))).thenReturn(fakePracticeId)

            mockMvc
                .perform(
                    post("/error-logbooks/challenges")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isOk)
                .andExpect(content().string(fakePracticeId))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return course outline when getting by course id`() {
            val fakeCourseId = "fakeCourseId"
            val fakeCourseOutlineId = "fakeCourseOutlineId"
            val fakeCourseOutline =
                CourseOutline(
                    courseOutlineId = fakeCourseOutlineId,
                    areas = listOf(Area(id = "areaId", name = "area", topics = emptyList(), iconUrl = "url")),
                )
            whenever(errorLogbookService.getErrorLogbookCourseOutline(fakeUserId, fakeCourseId)).thenReturn(
                fakeCourseOutline,
            )
            mockMvc
                .perform(
                    get("/error-logbooks/$fakeCourseId/outline")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.areas[0].id").value("areaId"))
                .andExpect(jsonPath("$.areas[0].name").value("area"))
                .andExpect(jsonPath("$.areas[0].iconUrl").value("url"))
                .andExpect(jsonPath("$.courseOutlineId").value(fakeCourseOutlineId))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return practice id when replay practice successfully`() {
            val fakePracticeId = "fakePracticeId"
            whenever(errorLogbookPracticeService.replayPractice(eq(fakeUserId), eq(fakePracticeId))).thenReturn("newPracticeId")

            mockMvc
                .perform(
                    post("/error-logbooks/practices/$fakePracticeId/replay")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isOk)
                .andExpect(content().string("newPracticeId"))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return course challenge status when given user id`() {
            val errorLogbookChallengeStatus =
                ErrorLogbookChallengeStatus(
                    cognitoUid = fakeUserId,
                    challengeCourse =
                        listOf(
                            CourseChallengeStatus("course id 1", true),
                            CourseChallengeStatus("course id 2", false),
                        ),
                )

            whenever(errorLogbookService.getErrorLogbookChallengeStatus(fakeUserId)).thenReturn(errorLogbookChallengeStatus)

            mockMvc
                .perform(get("/error-logbooks/challenge-status/me").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.challengeCourses[0].courseId").value("course id 1"))
                .andExpect(jsonPath("$.challengeCourses[0].shouldPopUp").value(true))
                .andExpect(jsonPath("$.challengeCourses[1].courseId").value("course id 2"))
                .andExpect(jsonPath("$.challengeCourses[1].shouldPopUp").value(false))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return empty course challenge status list when user error logbook challenge status is null`() {
            whenever(errorLogbookService.getErrorLogbookChallengeStatus(fakeUserId)).thenReturn(null)

            mockMvc
                .perform(get("/error-logbooks/challenge-status/me").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk)
                .andExpect(jsonPath<List<CourseChallengeStatus>>("$.challengeCourses", hasSize(0)))
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should disable course challenge status when user course challenge should pop up is true`() {
            val errorLogbookChallengeStatus =
                ErrorLogbookChallengeStatus(
                    cognitoUid = fakeUserId,
                    challengeCourse =
                        listOf(
                            CourseChallengeStatus("course id 1", true),
                            CourseChallengeStatus("course id 2", false),
                        ),
                )

            whenever(errorLogbookService.getErrorLogbookChallengeStatus(fakeUserId)).thenReturn(errorLogbookChallengeStatus)

            mockMvc
                .perform(get("/error-logbooks/challenge-notifications/me").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.challengeCourses[0].courseId").value("course id 1"))
                .andExpect(jsonPath("$.challengeCourses[0].shouldPopUp").value(true))
                .andExpect(jsonPath("$.challengeCourses[1].courseId").value("course id 2"))
                .andExpect(jsonPath("$.challengeCourses[1].shouldPopUp").value(false))

            verify(errorLogbookService, times(1)).disablePopUpOfChallengeStatus(fakeUserId, "course id 1")
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return pageable errorlogbook when given user id`() {
            val mockResolvedErrorLogbook =
                mock<ErrorLogbook> {
                    on { it.status } doReturn ErrorLogbookStatus.RESOLVED
                }
            val mockUnresolvedErrorLogbook =
                mock<ErrorLogbook> {
                    on { it.status } doReturn ErrorLogbookStatus.UNRESOLVED
                }
            val mockErrorLogbookOverview = listOf(mockResolvedErrorLogbook, mockUnresolvedErrorLogbook)

            val mockErrorLogbookOverviewPageable: Page<ErrorLogbook> = PageImpl(mockErrorLogbookOverview)

            whenever(errorLogbookService.getAllErrorLogbooks(fakeUserId, pageable = Pageable.ofSize(30))).thenReturn(mockErrorLogbookOverviewPageable)

            mockMvc.perform(
                get("/error-logbooks/pageable").contentType(MediaType.APPLICATION_JSON)
                    .param("courseId", "123")
                    .param("areaId", "456")
                    .param("topicId", "789")
                    .param("subtopicId", "abc")
                    .param("status", "COMPLETED")
                    .param("favorite", "true")
                    .param("errorCauses", "cause1", "cause2")
                    .param("sort", "DESC"),
            )
                .andExpect { status().isOk }
        }

        @Test
        @WithMockUser(username = "b89b0d17-4f15-4366-a6f6-398c7b0a38cf", roles = ["CONSUMER"])
        internal fun `should return unresolved error logbook count by course when get successfully`() {
            val courseId1 = "course-1"
            val courseId2 = "course-2"
            val countByCourse = mapOf(courseId1 to 5, courseId2 to 3)

            val course1 =
                Course(
                    id = courseId1,
                    code = "MATH101",
                    name = "Mathematics Grade 1",
                    grade = 1,
                    iconUrl = "icon1.png",
                    classroomIconUrl = "classroom1.png",
                    subjectId = "subject-1",
                )

            val course2 =
                Course(
                    id = courseId2,
                    code = "ENG101",
                    name = "English Grade 2",
                    grade = 2,
                    iconUrl = "icon2.png",
                    classroomIconUrl = "classroom2.png",
                    subjectId = "subject-2",
                )

            whenever(errorLogbookService.getUnresolvedErrorLogbookCountByCourse(fakeUserId)).thenReturn(countByCourse)
            whenever(courseService.getCourse(courseId1)).thenReturn(course1)
            whenever(courseService.getCourse(courseId2)).thenReturn(course2)

            mockMvc
                .perform(get("/error-logbooks/unresolved-count-by-course/me").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk)
                .andExpect(jsonPath("$[0].courseId").value(courseId1))
                .andExpect(jsonPath("$[0].courseName").value("Mathematics Grade 1"))
                .andExpect(jsonPath("$[0].courseGrade").value(1))
                .andExpect(jsonPath("$[0].unresolvedCount").value(5))
                .andExpect(jsonPath("$[1].courseId").value(courseId2))
                .andExpect(jsonPath("$[1].courseName").value("English Grade 2"))
                .andExpect(jsonPath("$[1].courseGrade").value(2))
                .andExpect(jsonPath("$[1].unresolvedCount").value(3))

            verify(errorLogbookService, times(1)).getUnresolvedErrorLogbookCountByCourse(fakeUserId)
            verify(courseService, times(1)).getCourse(courseId1)
            verify(courseService, times(1)).getCourse(courseId2)
        }
    }
