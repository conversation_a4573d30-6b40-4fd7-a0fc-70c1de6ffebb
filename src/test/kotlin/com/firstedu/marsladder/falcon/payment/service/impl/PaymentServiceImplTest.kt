package com.firstedu.marsladder.falcon.payment.service.impl

import com.firstedu.marsladder.falcon.infrastructure.stripe.StripeClient
import com.firstedu.marsladder.falcon.infrastructure.stripe.StripeInvoiceResult
import com.firstedu.marsladder.falcon.infrastructure.stripe.StripePaymentMethodResult
import com.firstedu.marsladder.falcon.infrastructure.stripe.StripePaymentResult
import com.firstedu.marsladder.falcon.infrastructure.stripe.SubscriptionResult
import com.firstedu.marsladder.falcon.payment.service.domain.StripeInvoice
import com.firstedu.marsladder.falcon.payment.service.domain.StripePayment
import com.firstedu.marsladder.falcon.payment.service.domain.StripePaymentMethod
import com.firstedu.marsladder.falcon.payment.service.domain.StripeTeacherPayment
import com.firstedu.marsladder.falcon.payment.service.exception.PaymentNotFoundException
import com.firstedu.marsladder.falcon.payment.type.SubscriptionItemSource
import com.firstedu.marsladder.falcon.seatSubscription.repository.SeatSubscriptionRepository
import com.firstedu.marsladder.falcon.seatSubscription.repository.SubscribedSeatRepository
import com.firstedu.marsladder.falcon.seatSubscription.repository.entity.SeatHistoryEntity
import com.firstedu.marsladder.falcon.seatSubscription.repository.entity.SeatSubscriptionEntity
import com.firstedu.marsladder.falcon.seatSubscription.repository.entity.SubscribedSeatEntity
import com.firstedu.marsladder.falcon.subscription.fixtures.SubscriptionEntityFixtureBuilder
import com.firstedu.marsladder.falcon.subscription.repository.CustomerRepository
import com.firstedu.marsladder.falcon.subscription.repository.SubscriptionRepository
import com.firstedu.marsladder.falcon.subscription.repository.entity.CustomerEntity
import com.firstedu.marsladder.falcon.subscription.repository.entity.SubscriptionEntity
import com.firstedu.marsladder.falcon.subscription.service.exception.CustomerNotFoundException
import com.firstedu.marsladder.falcon.subscription.service.exception.InvalidSubscriptionOperationException
import com.firstedu.marsladder.falcon.subscription.service.exception.StripeCustomerNotFoundException
import com.firstedu.marsladder.falcon.subscription.service.exception.SubscriptionNotFoundException
import com.firstedu.marsladder.falcon.subscription.type.SubscriptionFrequency
import com.firstedu.marsladder.falcon.subscription.type.SubscriptionStatus
import com.firstedu.marsladder.falcon.teachersubscription.fixtures.TeacherSubscriptionEntityFixtureBuilder
import com.firstedu.marsladder.falcon.teachersubscription.repository.TeacherSubscriptionRepository
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.doNothing
import com.nhaarman.mockitokotlin2.eq
import com.nhaarman.mockitokotlin2.whenever
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.springframework.core.env.Environment
import java.time.LocalDateTime
import java.util.Optional

@ExtendWith(MockitoExtension::class)
internal class PaymentServiceImplTest {
    @InjectMocks
    private lateinit var paymentService: PaymentServiceImpl

    @Mock
    private lateinit var customerRepository: CustomerRepository

    @Mock
    private lateinit var subscriptionRepository: SubscriptionRepository

    @Mock
    private lateinit var teacherSubscriptionRepository: TeacherSubscriptionRepository

    @Mock
    private lateinit var seatSubscriptionRepository: SeatSubscriptionRepository

    @Mock
    private lateinit var subscribedSeatRepository: SubscribedSeatRepository

    @Mock
    private lateinit var stripeClient: StripeClient

    @Mock
    private lateinit var environment: Environment

    private val mockCognitoUid = "Mock Cognito ID"
    private val mockCustomerId = "Mock Customer ID"
    private val mockSubscriptionId = "Mock Subscription ID"
    private val mockStripeSubscriptionId = "Mock Stripe Subscription ID"
    private val mockPaymentMethodId = "Stripe Payment Method ID"
    private val mockPaymentId = "Mock Payment ID"
    private val paymentCreatedAt = LocalDateTime.of(2021, 11, 26, 18, 23, 47)

    private val mockCustomer =
        CustomerEntity(
            id = "Customer ID",
            cognitoUid = mockCognitoUid,
            customerId = mockCustomerId,
            currency = "aud",
        )
    private val mockStripePaymentResult =
        StripePaymentResult(
            id = "Stripe Payment Result",
            amount = "100.0",
            currency = "AUD",
            paymentMethod = mockPaymentMethodId,
            status = "succeeded",
            created = paymentCreatedAt,
            invoice =
                StripeInvoiceResult(
                    id = "id",
                    number = "number",
                    invoicePdf = "invoice pdf",
                    receiptNumber = "receipt number",
                    paid = true,
                    subscriptionId = mockStripeSubscriptionId,
                    status = "status",
                    hostedInvoiceUrl = "hosted invoice url",
                    subtotal = "100",
                    tax = "10",
                ),
        )
    private val updatedInvoice = mockStripePaymentResult.invoice.copy(subscriptionId = mockSubscriptionId)
    private val mockStripePaymentMethodResult =
        StripePaymentMethodResult(
            id = mockPaymentMethodId,
            type = "card",
            brand = "brand",
            country = "AU",
            funding = "funding",
            last4 = "1234",
        )
    private val mockSubscriptionEntity = SubscriptionEntityFixtureBuilder().build()

    @Test
    internal fun `should return all payments when subscription exists`() {
        whenever(customerRepository.findByCognitoUidAndCurrency(eq(mockCognitoUid), eq("aud"))).thenReturn(Optional.of(mockCustomer))
        whenever(stripeClient.getPayments(eq(mockCustomerId))).thenReturn(listOf(mockStripePaymentResult))
        whenever(stripeClient.getPaymentMethod(eq(mockPaymentMethodId))).thenReturn(mockStripePaymentMethodResult)
        whenever(
            subscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(Optional.of(SubscriptionEntityFixtureBuilder().withId(mockSubscriptionId).withStartAt(paymentCreatedAt).build()))

        Assertions.assertEquals(
            listOf(
                StripePayment.from(
                    mockStripePaymentResult,
                    mockStripePaymentMethodResult,
                    SubscriptionItemSource.COURSE,
                    paymentCreatedAt.plusMonths(1),
                    null,
                    0,
                )
                    .copy(invoice = StripeInvoice.from(updatedInvoice)),
            ),
            paymentService.getPayments(mockCognitoUid, SubscriptionItemSource.COURSE, "aud"),
        )
    }

    @Test
    internal fun `should return all payments when no subscription but seat subscription exists`() {
        whenever(environment.activeProfiles).thenReturn(arrayOf("test"))
        whenever(customerRepository.findByCognitoUidAndCurrency(eq(mockCognitoUid), eq("aud"))).thenReturn(Optional.of(mockCustomer))
        whenever(stripeClient.getPayments(eq(mockCustomerId))).thenReturn(listOf(mockStripePaymentResult))
        whenever(stripeClient.getPaymentMethod(eq(mockPaymentMethodId))).thenReturn(mockStripePaymentMethodResult)
        whenever(
            subscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(Optional.empty())
        whenever(
            seatSubscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(SeatSubscriptionEntity(id = mockSubscriptionId, startAt = paymentCreatedAt, frequency = SubscriptionFrequency.MONTHLY))
        whenever(subscribedSeatRepository.findBySubscriptionId(mockSubscriptionId)).thenReturn(
            SubscribedSeatEntity(
                subscriptionId = mockSubscriptionId,
                seats = 1,
                frequency = SubscriptionFrequency.MONTHLY,
                seatsHistory =
                    listOf(
                        SeatHistoryEntity(
                            createdAt = paymentCreatedAt.minusMonths(2),
                            seats = 5,
                            frequency = SubscriptionFrequency.MONTHLY,
                        ),
                        SeatHistoryEntity(
                            createdAt = paymentCreatedAt.minusMonths(1),
                            seats = 4,
                            frequency = SubscriptionFrequency.MONTHLY,
                        ),
                        SeatHistoryEntity(createdAt = paymentCreatedAt, seats = 3, frequency = SubscriptionFrequency.MONTHLY),
                        SeatHistoryEntity(createdAt = paymentCreatedAt.plusMonths(1), seats = 2, frequency = SubscriptionFrequency.MONTHLY),
                        SeatHistoryEntity(createdAt = paymentCreatedAt.plusMonths(2), seats = 1, frequency = SubscriptionFrequency.MONTHLY),
                    ),
            ),
        )

        Assertions.assertEquals(
            listOf(
                StripePayment.from(
                    mockStripePaymentResult,
                    mockStripePaymentMethodResult,
                    SubscriptionItemSource.SEAT,
                    paymentCreatedAt.plusMonths(1),
                    2,
                    0,
                )
                    .copy(invoice = StripeInvoice.from(updatedInvoice)),
            ),
            paymentService.getPayments(mockCognitoUid, SubscriptionItemSource.SEAT, "aud"),
        )
    }

    @Test
    internal fun `should return empty payments when get course payments but no subscription`() {
        whenever(environment.activeProfiles).thenReturn(arrayOf("test"))
        whenever(customerRepository.findByCognitoUidAndCurrency(eq(mockCognitoUid), eq("aud"))).thenReturn(Optional.of(mockCustomer))
        whenever(stripeClient.getPayments(eq(mockCustomerId))).thenReturn(listOf(mockStripePaymentResult))
        whenever(stripeClient.getPaymentMethod(eq(mockPaymentMethodId))).thenReturn(mockStripePaymentMethodResult)
        whenever(
            subscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(Optional.empty())
        whenever(
            seatSubscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(SeatSubscriptionEntity(id = mockSubscriptionId, startAt = paymentCreatedAt, frequency = SubscriptionFrequency.MONTHLY))
        whenever(subscribedSeatRepository.findBySubscriptionId(mockSubscriptionId)).thenReturn(
            SubscribedSeatEntity(
                subscriptionId = mockSubscriptionId,
                seats = 1,
                frequency = SubscriptionFrequency.MONTHLY,
                seatsHistory =
                    listOf(
                        SeatHistoryEntity(createdAt = paymentCreatedAt.plusMonths(2), seats = 1, frequency = SubscriptionFrequency.MONTHLY),
                        SeatHistoryEntity(createdAt = paymentCreatedAt.plusMonths(1), seats = 2, frequency = SubscriptionFrequency.MONTHLY),
                        SeatHistoryEntity(createdAt = paymentCreatedAt, seats = 3, frequency = SubscriptionFrequency.MONTHLY),
                        SeatHistoryEntity(
                            createdAt = paymentCreatedAt.minusMonths(1),
                            seats = 4,
                            frequency = SubscriptionFrequency.MONTHLY,
                        ),
                        SeatHistoryEntity(
                            createdAt = paymentCreatedAt.minusMonths(2),
                            seats = 5,
                            frequency = SubscriptionFrequency.MONTHLY,
                        ),
                    ),
            ),
        )

        Assertions.assertEquals(
            emptyList<StripePayment>(),
            paymentService.getPayments(mockCognitoUid, SubscriptionItemSource.COURSE, "aud"),
        )
    }

    @Test
    internal fun `should return empty payments when get seat payments but no seat subscription`() {
        whenever(customerRepository.findByCognitoUidAndCurrency(eq(mockCognitoUid), eq("aud"))).thenReturn(Optional.of(mockCustomer))
        whenever(stripeClient.getPayments(eq(mockCustomerId))).thenReturn(listOf(mockStripePaymentResult))
        whenever(stripeClient.getPaymentMethod(eq(mockPaymentMethodId))).thenReturn(mockStripePaymentMethodResult)
        whenever(
            subscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(Optional.of(SubscriptionEntityFixtureBuilder().withId(mockSubscriptionId).withStartAt(paymentCreatedAt).build()))

        Assertions.assertEquals(
            emptyList<StripePayment>(),
            paymentService.getPayments(mockCognitoUid, SubscriptionItemSource.SEAT, "aud"),
        )
    }

    @Test
    internal fun `should throw not found exception when get payments and no subscription found in db for prod env`() {
        whenever(environment.activeProfiles).thenReturn(arrayOf("prod"))
        whenever(customerRepository.findByCognitoUidAndCurrency(eq(mockCognitoUid), eq("aud"))).thenReturn(Optional.of(mockCustomer))
        whenever(stripeClient.getPayments(eq(mockCustomerId))).thenReturn(listOf(mockStripePaymentResult))
        whenever(
            subscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(Optional.empty())
        whenever(
            seatSubscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(null)

        assertThrows<SubscriptionNotFoundException> {
            paymentService.getPayments(mockCognitoUid, SubscriptionItemSource.SEAT, "aud")
        }
    }

    @Test
    internal fun `should return empty list when get payments and no subscription found in db for test env`() {
        whenever(environment.activeProfiles).thenReturn(arrayOf("test"))
        whenever(customerRepository.findByCognitoUidAndCurrency(eq(mockCognitoUid), eq("aud"))).thenReturn(Optional.of(mockCustomer))
        whenever(stripeClient.getPayments(eq(mockCustomerId))).thenReturn(listOf(mockStripePaymentResult))
        whenever(
            subscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(Optional.empty())
        whenever(
            seatSubscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(null)

        Assertions.assertEquals(emptyList<StripePayment>(), paymentService.getPayments(mockCognitoUid, SubscriptionItemSource.SEAT, "aud"))
    }

    @Test
    internal fun `should return empty list when get payments but no customer exists`() {
        whenever(customerRepository.findByCognitoUidAndCurrency(eq(mockCognitoUid), eq("aud"))).thenReturn(Optional.empty())

        Assertions.assertEquals(
            emptyList<StripePayment>(),
            paymentService.getPayments(mockCognitoUid, SubscriptionItemSource.COURSE, "aud"),
        )
    }

    @Test
    internal fun `should return payment when subscription exists`() {
        whenever(customerRepository.findByCognitoUid(eq(mockCognitoUid))).thenReturn(listOf(mockCustomer))
        whenever(stripeClient.getPayment(eq(mockPaymentId))).thenReturn(mockStripePaymentResult)
        whenever(stripeClient.getPaymentMethod(eq(mockPaymentMethodId))).thenReturn(mockStripePaymentMethodResult)
        whenever(
            subscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(Optional.of(SubscriptionEntityFixtureBuilder().withId(mockSubscriptionId).withStartAt(paymentCreatedAt).build()))

        Assertions.assertEquals(
            StripePayment.from(
                mockStripePaymentResult,
                mockStripePaymentMethodResult,
                SubscriptionItemSource.COURSE,
                paymentCreatedAt.plusMonths(1),
                null,
                0,
            )
                .copy(invoice = StripeInvoice.from(updatedInvoice)),
            paymentService.getPayment(mockCognitoUid, mockPaymentId),
        )
    }

    @Test
    internal fun `should return payment when no subscription but seat subscription exists`() {
        whenever(environment.activeProfiles).thenReturn(arrayOf("test"))
        whenever(customerRepository.findByCognitoUid(eq(mockCognitoUid))).thenReturn(listOf(mockCustomer))
        whenever(stripeClient.getPayment(eq(mockPaymentId))).thenReturn(mockStripePaymentResult)
        whenever(stripeClient.getPaymentMethod(eq(mockPaymentMethodId))).thenReturn(mockStripePaymentMethodResult)
        whenever(
            subscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(Optional.empty())
        whenever(
            seatSubscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(SeatSubscriptionEntity(id = mockSubscriptionId, startAt = paymentCreatedAt, frequency = SubscriptionFrequency.MONTHLY))
        whenever(subscribedSeatRepository.findBySubscriptionId(mockSubscriptionId)).thenReturn(
            SubscribedSeatEntity(
                subscriptionId = mockSubscriptionId,
                seats = 1,
                frequency = SubscriptionFrequency.MONTHLY,
                seatsHistory =
                    listOf(
                        SeatHistoryEntity(
                            createdAt = paymentCreatedAt.minusMonths(2),
                            seats = 5,
                            frequency = SubscriptionFrequency.MONTHLY,
                        ),
                        SeatHistoryEntity(
                            createdAt = paymentCreatedAt.minusMonths(1),
                            seats = 4,
                            frequency = SubscriptionFrequency.MONTHLY,
                        ),
                        SeatHistoryEntity(createdAt = paymentCreatedAt, seats = 3, frequency = SubscriptionFrequency.MONTHLY),
                        SeatHistoryEntity(createdAt = paymentCreatedAt.plusMonths(1), seats = 2, frequency = SubscriptionFrequency.MONTHLY),
                        SeatHistoryEntity(createdAt = paymentCreatedAt.plusMonths(2), seats = 1, frequency = SubscriptionFrequency.MONTHLY),
                    ),
            ),
        )

        Assertions.assertEquals(
            StripePayment.from(
                mockStripePaymentResult,
                mockStripePaymentMethodResult,
                SubscriptionItemSource.SEAT,
                paymentCreatedAt.plusMonths(1),
                2,
                0,
            )
                .copy(invoice = StripeInvoice.from(updatedInvoice)),
            paymentService.getPayment(mockCognitoUid, mockPaymentId),
        )
    }

    @Test
    internal fun `should throw subscription not found exception when get payment and no subscription found in db for prod env`() {
        whenever(environment.activeProfiles).thenReturn(arrayOf("prod"))
        whenever(customerRepository.findByCognitoUid(eq(mockCognitoUid))).thenReturn(listOf(mockCustomer))
        whenever(stripeClient.getPayment(eq(mockPaymentId))).thenReturn(mockStripePaymentResult)
        whenever(
            subscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(Optional.empty())
        whenever(
            seatSubscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(null)

        assertThrows<SubscriptionNotFoundException> {
            paymentService.getPayment(mockCognitoUid, mockPaymentId)
        }
    }

    @Test
    internal fun `should throw payment not found exception when get payment and no subscription found in db for test env`() {
        whenever(environment.activeProfiles).thenReturn(arrayOf("test"))
        whenever(customerRepository.findByCognitoUid(eq(mockCognitoUid))).thenReturn(listOf(mockCustomer))
        whenever(stripeClient.getPayment(eq(mockPaymentId))).thenReturn(mockStripePaymentResult)
        whenever(
            subscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(Optional.empty())
        whenever(
            seatSubscriptionRepository.findByCognitoUidAndStripeSubscriptionId(
                eq(mockCognitoUid),
                eq(mockStripeSubscriptionId),
            ),
        ).thenReturn(null)

        assertThrows<PaymentNotFoundException> {
            paymentService.getPayment(mockCognitoUid, mockPaymentId)
        }
    }

    @Test
    internal fun `should throw customer not fount exception when get payment but no customer exists`() {
        whenever(customerRepository.findByCognitoUid(eq(mockCognitoUid))).thenReturn(emptyList())

        assertThrows<StripeCustomerNotFoundException> {
            paymentService.getPayment(mockCognitoUid, mockPaymentId)
        }
    }

    @Test
    internal fun `should update the payment method`() {
        whenever(customerRepository.findByCognitoUidAndCurrency(eq(mockCognitoUid), eq("aud"))).thenReturn(Optional.of(mockCustomer))
        whenever(
            subscriptionRepository.findByCognitoUidAndId(eq(mockCognitoUid), eq(mockSubscriptionId)),
        ).thenReturn(Optional.of(mockSubscriptionEntity))
        whenever(
            stripeClient.getSubscription(eq(mockSubscriptionEntity.stripeSubscriptionId!!)),
        ).thenReturn(
            SubscriptionResult(
                lastPaymentTime = LocalDateTime.now(),
                nextPaymentTime = LocalDateTime.now().plusDays(1),
                status = "active",
                currency = "aud",
            ),
        )
        whenever(subscriptionRepository.save(any<SubscriptionEntity>())).thenReturn(mockSubscriptionEntity)
        doNothing().whenever(stripeClient).attachPaymentMethod(eq(mockPaymentMethodId), eq(mockCustomerId))
        doNothing().whenever(stripeClient).updatePaymentMethod(eq(mockPaymentMethodId), eq(mockSubscriptionEntity.stripeSubscriptionId!!))

        assertDoesNotThrow {
            paymentService.updatePaymentMethod(mockCognitoUid, mockSubscriptionId, mockPaymentMethodId)
        }
    }

    @Test
    internal fun `should throw invalid subscription operation exception when update the payment method for cancelled subscription`() {
        whenever(subscriptionRepository.findByCognitoUidAndId(eq(mockCognitoUid), eq(mockSubscriptionId))).thenReturn(
            Optional.of(SubscriptionEntityFixtureBuilder().withStatus(SubscriptionStatus.CANCELLED).build()),
        )

        assertThrows<InvalidSubscriptionOperationException> {
            paymentService.updatePaymentMethod(mockCognitoUid, mockSubscriptionId, mockPaymentMethodId)
        }
    }

    @Test
    internal fun `should throw customer not found exception when update payment method but no customer exists`() {
        val subscriptionEntity = SubscriptionEntityFixtureBuilder().withStatus(SubscriptionStatus.SUCCESS).build()
        whenever(
            subscriptionRepository.findByCognitoUidAndId(eq(mockCognitoUid), eq(mockSubscriptionId)),
        ).thenReturn(Optional.of(subscriptionEntity))
        whenever(
            stripeClient.getSubscription(eq(subscriptionEntity.stripeSubscriptionId!!)),
        ).thenReturn(
            SubscriptionResult(
                lastPaymentTime = LocalDateTime.now(),
                nextPaymentTime = LocalDateTime.now().plusDays(1),
                status = "active",
                currency = "aud",
            ),
        )
        whenever(customerRepository.findByCognitoUidAndCurrency(eq(mockCognitoUid), eq("aud"))).thenReturn(Optional.empty())

        assertThrows<CustomerNotFoundException> {
            paymentService.updatePaymentMethod(mockCognitoUid, mockSubscriptionId, mockPaymentMethodId)
        }
    }

    @Test
    internal fun `should throw subscription not found exception when update payment method but no subscription exists`() {
        whenever(subscriptionRepository.findByCognitoUidAndId(eq(mockCognitoUid), eq(mockSubscriptionId))).thenReturn(Optional.empty())

        assertThrows<SubscriptionNotFoundException> {
            paymentService.updatePaymentMethod(mockCognitoUid, mockSubscriptionId, mockPaymentMethodId)
        }
    }

    @Test
    internal fun `should return all payment methods`() {
        whenever(customerRepository.findByCognitoUid(eq(mockCognitoUid))).thenReturn(listOf(mockCustomer))
        whenever(stripeClient.getPaymentMethods(eq(mockCustomerId))).thenReturn(listOf(mockStripePaymentMethodResult))

        Assertions.assertEquals(
            listOf(StripePaymentMethod.from(mockStripePaymentMethodResult)),
            paymentService.getPaymentMethods(mockCognitoUid),
        )
    }

    @Test
    internal fun `should return teacher payments when get teacher payments`() {
        whenever(customerRepository.findByCognitoUidAndCurrency(eq(mockCognitoUid), eq("aud"))).thenReturn(Optional.of(mockCustomer))
        whenever(stripeClient.getPayments(eq(mockCustomerId))).thenReturn(listOf(mockStripePaymentResult))

        whenever(teacherSubscriptionRepository.findByCognitoUidAndStripeSubscriptionId(mockCognitoUid, mockStripeSubscriptionId))
            .thenReturn(
                Optional.of(
                    TeacherSubscriptionEntityFixtureBuilder().withId(mockSubscriptionId).withStripeSubscriptionId(mockStripeSubscriptionId).withCreatedAt(paymentCreatedAt).withStartAt(paymentCreatedAt).build(),
                ),
            )

        whenever(stripeClient.getPaymentMethod(mockPaymentMethodId))
            .thenReturn(mockStripePaymentMethodResult)

        Assertions.assertEquals(
            listOf(
                StripeTeacherPayment.from(
                    mockStripePaymentResult,
                    mockStripePaymentMethodResult,
                    paymentCreatedAt.plusMonths(1),
                    SubscriptionFrequency.MONTHLY,
                ).copy(invoice = StripeInvoice.from(updatedInvoice)),
            ),
            paymentService.getTeacherPayments(mockCognitoUid, "aud"),
        )
    }

    @Test
    internal fun `should return empty payments when get teacher payments but no teacher subscription`() {
        whenever(customerRepository.findByCognitoUidAndCurrency(eq(mockCognitoUid), eq("aud"))).thenReturn(Optional.of(mockCustomer))
        whenever(stripeClient.getPayments(eq(mockCustomerId))).thenReturn(listOf(mockStripePaymentResult))

        whenever(teacherSubscriptionRepository.findByCognitoUidAndStripeSubscriptionId(mockCognitoUid, mockStripeSubscriptionId))
            .thenReturn(Optional.empty())

        Assertions.assertEquals(
            emptyList<StripeTeacherPayment>(),
            paymentService.getTeacherPayments(mockCognitoUid, "aud"),
        )
    }
}
